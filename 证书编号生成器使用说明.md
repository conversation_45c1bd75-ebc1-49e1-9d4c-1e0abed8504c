# 奢侈品鉴别证书编号生成器

## 概述

本系统为奢侈品鉴别业务提供全局唯一的14位数字证书编号生成功能，编号包含业务含义且不易被破解猜测。

## 编号格式

证书编号由14位数字组成，格式如下：

```
XX YYMMDD MM RRR C
```

- **XX (2位)**: 业务类型标识
  - `01`: 奢侈品鉴别业务
  
- **YYMMDD (6位)**: 日期信息
  - `YY`: 年份后两位
  - `MM`: 月份
  - `DD`: 日期
  
- **MM (2位)**: 时间标识
  - 基于当天总分钟数的后两位，提供时间唯一性
  
- **RRR (3位)**: 随机数
  - 000-999的随机数，增加唯一性
  
- **C (1位)**: 校验位
  - 基于前13位计算的校验位，防止篡改

## 使用方法

### 1. 生成证书编号

```java
import com.qudian.idle.inspection.app.business.util.CertificateGenerator;

// 生成14位证书编号
String certificateNo = CertificateGenerator.generateCertificateNo();
System.out.println("证书编号: " + certificateNo);
// 输出示例: 01250822749763
```

### 2. 生成防伪扣编号

```java
// 生成12位防伪扣编号
String antiCounterfeitNo = CertificateGenerator.generateAntiCounterfeitNo();
System.out.println("防伪扣编号: " + antiCounterfeitNo);
// 输出示例: 250822111443
```

### 3. 验证证书编号

```java
// 验证证书编号格式和校验位
boolean isValid = CertificateGenerator.validateCertificateNo("01250822749763");
System.out.println("验证结果: " + isValid);
```

### 4. 解析时间信息

```java
// 从证书编号中解析时间信息
String timestamp = CertificateGenerator.parseTimestamp("01250822749763");
System.out.println("时间信息: " + timestamp);
// 输出示例: 2025-08-22 (时间标识: 74)
```

## 业务服务集成

在 `CertificateServiceImpl` 中的使用示例：

```java
@Override
public void generate(CertificateGeneratesCmd generatesCmd) {
    // 生成证书编号14位
    String certificateNo = CertificateGenerator.generateCertificateNo();
    
    // 生成防伪扣编号12位
    String antiCounterfeitNo = CertificateGenerator.generateAntiCounterfeitNo();
    
    // 创建证书记录
    CertificatePO certificatePO = new CertificatePO()
            .setCertificateNo(certificateNo)
            .setAntiCounterfeitNo(antiCounterfeitNo)
            .setVerdict(generatesCmd.getVerdict())
            .setSn(generatesCmd.getSn())
            .setOrderNo(generatesCmd.getOrderNo())
            .setSkuId(generatesCmd.getSkuId());
    
    // 保存证书记录
    certificateRepository.save(certificatePO);
    
    log.info("Certificate generated successfully. CertificateNo: {}, AntiCounterfeitNo: {}, SN: {}", 
            certificateNo, antiCounterfeitNo, generatesCmd.getSn());
}
```

## 特性

### 1. 全局唯一性
- 通过时间戳 + 随机数 + 校验位确保唯一性
- 支持高并发场景下的唯一性保证

### 2. 业务含义
- 编号包含业务类型、时间信息等有意义的内容
- 便于业务追溯和管理

### 3. 安全性
- 使用校验位防止篡改
- 随机数部分增加破解难度
- 时间标识采用非直观的分钟数取模方式

### 4. 可扩展性
- 业务类型标识支持扩展到其他业务
- 校验算法可根据需要调整

## 示例输出

```
证书编号: 01250822749763
├── 业务类型: 01 (奢侈品鉴别)
├── 日期: 250822 (2025年8月22日)
├── 时间标识: 74 (当天分钟数标识)
├── 随机数: 976 (3位随机)
└── 校验位: 3 (防篡改)

防伪扣编号: 250822111443
├── 日期: 250822 (2025年8月22日)
└── 时间: 111443 (11时14分43秒)
```

## 注意事项

1. **时间同步**: 确保服务器时间准确，避免时间戳错误
2. **并发处理**: 在高并发场景下，随机数可能重复，建议结合数据库唯一约束
3. **校验位验证**: 在接收外部证书编号时，务必进行格式和校验位验证
4. **日志记录**: 生成过程会自动记录日志，便于问题排查

## 测试

项目包含完整的单元测试，覆盖以下场景：
- 基本格式验证
- 唯一性测试
- 并发安全性测试
- 校验位验证
- 时间戳解析

运行测试：
```bash
mvn test -Dtest=CertificateGeneratorTest
```
