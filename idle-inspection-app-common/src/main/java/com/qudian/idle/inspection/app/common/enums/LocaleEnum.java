package com.qudian.idle.inspection.app.common.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Locale;

/**
 * <p>文件名称:com.qudian.lme.driver.api.enums.locale</p>
 * <p>文件描述:</p>
 * <p>版权所有: 版权所有(C)2019-2099</p>
 * <p>公 司: 趣店 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">kangjun</a>
 * @version 1.0
 * @since 2023/5/19 11:19 上午
 */
@AllArgsConstructor
@Getter
public enum LocaleEnum {
    ZH_CN("zh-CN", "中文简体"),
    EN_US("en-US", "英文"),
    ES_ES("es-ES", "西班牙语"),;

    public final String code;
    public final String desc;

    public static LocaleEnum getLocaleByCode(String code) {
        return Arrays.stream(LocaleEnum.values()).
                filter(localeEnum -> localeEnum.code.equals(code)).
                findFirst().
                orElse(EN_US);
    }

    public static LocaleEnum getLocaleByCodeNotDefault(String code) {
        return Arrays.stream(LocaleEnum.values()).
                filter(localeEnum -> localeEnum.code.equals(code)).
                findFirst().
                orElse(null);
    }

    public static Locale getLocale(String countryCode) {
        LocaleEnum localeEnum;
        if ("ES".equalsIgnoreCase(countryCode)) {
            localeEnum = LocaleEnum.ES_ES;
        } else if ("CN".equalsIgnoreCase(countryCode)) {
            localeEnum = LocaleEnum.ZH_CN;
        }else{
            localeEnum = LocaleEnum.EN_US;
        }
        String[] langCountry = localeEnum.code.split("-");
        return new Locale(langCountry[0], langCountry[1]);
    }

}
