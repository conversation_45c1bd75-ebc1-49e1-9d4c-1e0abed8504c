package com.qudian.idle.inspection.app.common.utils.http;

import com.alibaba.fastjson.JSON;
import com.qudian.lme.base.vo.BaseResponseVo;
import com.qudian.pdt.toolkit.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.HttpUrl;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;import com.alibaba.fastjson.JSON;
import com.qudian.lme.base.vo.BaseResponseVo;
import com.qudian.pdt.toolkit.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.HttpUrl;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.FormBody;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.RequestBody;
import okhttp3.ResponseBody;

import java.io.IOException;
import java.rmi.ServerException;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2024</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> board)
 * @version 1.0
 */
@Slf4j
public class HttpClient {
    private final OkHttpClient okHttpClient;
    private String username;
    private String password;
    public static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");


    public HttpClient(long connectionTimeout, long readTimeout, String username, String password) {
        this.username = username;
        this.password = password;
        this.okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(connectionTimeout, TimeUnit.MILLISECONDS)
                .readTimeout(readTimeout, TimeUnit.MILLISECONDS)
                .build();
    }

    public HttpClient(long connectionTimeout, long readTimeout) {
        this.okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(connectionTimeout, TimeUnit.MILLISECONDS)
                .readTimeout(readTimeout, TimeUnit.MILLISECONDS)
                .build();
    }

    private String getResultStr(Response response, String url) throws IOException {
        String result = null;
        if (!response.isSuccessful()) {
            ResponseBody responseBody = response.body();
            if (responseBody != null) {
                result = responseBody.string();
            }
            log.error("POST {},code={},res={}", url, response.code(), result);
            String message = String.format("http post error, code=%s, message=%s, url=%s",
                    response.code(), response.message(), response.request().url());
            throw new ServerException(message);
        }
        assert response.body() != null;
        return response.body().string();
    }


    /**
     * get请求，同步方式，获取网络数据，是在主线程中执行的，需要新起线程，将其放到子线程中执行
     *
     * @param url
     * @return
     */
    public String getData(String url, Map<String, String> params) {
        //1 构造Request
        HttpUrl.Builder httpBuilder = HttpUrl.parse(url).newBuilder();
        if (params != null) {
            for (Map.Entry<String, String> param : params.entrySet()) {
                httpBuilder.addQueryParameter(param.getKey(), param.getValue());
            }
        }
        Request request = new Request.Builder().get().url(httpBuilder.build()).build();
        //2 将Request封装为Call
        Call call = okHttpClient.newCall(request);
        //3 执行Call，得到response
        String result = null;
        try {
            Response response = call.execute();
            result = getResultStr(response, url);

        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * get请求，同步方式，获取网络数据，是在主线程中执行的，需要新起线程，将其放到子线程中执行
     *
     * @param url
     * @return
     */
    public String getData(String url) {
        //1 构造Request
        Request.Builder builder = new Request.Builder();
        Request request = builder.get().url(url).build();
        //2 将Request封装为Call
        Call call = okHttpClient.newCall(request);
        //3 执行Call，得到response
        String result = null;
        try {
            Response response = call.execute();
            result = getResultStr(response, url);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * post请求，同步方式，提交数据，是在主线程中执行的，需要新起线程，将其放到子线程中执行
     *
     * @param url
     * @param bodyParams
     * @return
     */
    public String postData(String url, Map<String, String> bodyParams) {
        //1构造RequestBody
        RequestBody body = setRequestBody(bodyParams);
        //2 构造Request
        Request.Builder requestBuilder = new Request.Builder();
        Request request = requestBuilder.post(body).url(url).build();
        //3 将Request封装为Call
        Call call = okHttpClient.newCall(request);
        //4 执行Call，得到response
        Response response = null;
        String result = null;
        try {
            response = call.execute();
            result = getResultStr(response, url);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * post请求，同步方式，提交数据，是在主线程中执行的，需要新起线程，将其放到子线程中执行
     *
     * @param url
     * @param bodyParams
     * @return
     */
    public String postJsonData(String url, String bodyParams) {
        //1构造RequestBody
        RequestBody body = RequestBody.create(JSON, bodyParams);
        //2 构造Request
        Request.Builder requestBuilder = new Request.Builder();
        Request request = requestBuilder.post(body).url(url).build();
        //3 将Request封装为Call
        Call call = okHttpClient.newCall(request);
        //4 执行Call，得到response
        Response response = null;
        String result = null;
        try {
            response = call.execute();
            result = getResultStr(response, url);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }


    /**
     * post的请求参数，构造RequestBody
     *
     * @param bodyParams
     * @return
     */
    private RequestBody setRequestBody(Map<String, String> bodyParams) {
        RequestBody body;
        FormBody.Builder formEncodingBuilder = new FormBody.Builder();
        if (bodyParams != null) {
            Iterator<String> iterator = bodyParams.keySet().iterator();
            String key;
            while (iterator.hasNext()) {
                key = iterator.next();
                formEncodingBuilder.add(key, bodyParams.get(key));
            }
        }
        body = formEncodingBuilder.build();
        return body;

    }


}
