package com.qudian.idle.inspection.app.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <p>文件名称:com.qudian.lme.driver.common.config.ToolRemoteApiConfig</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2022/11/10
 */
@Data
@Configuration
@ConfigurationProperties("remote.tool")
public class ToolRemoteConfig {
    /**
     * 本地："http://lme-dev-tool-srv.quplusplus.net"
     */
    private String host;
    /**
     * 短信发送
     */
    private String smsNotify = "/v1/sms/send";


    /**
     * oss,gcs,s3
     */
    private String storeProvider = "s3";

    /**
     * 加签
     */
    private String signedUrl = "/v1/storage/signedurl/unified";
    /**
     * 批量加签
     */
    private String batchSignedUrl = "/v1/storage/batchSignedurl";


    /**
     * 校验电话号码格式
     */
    private String checkPhone = "/v1/sms/phone/check";

    /**
     * 获取多语言
     */
    private String languageList = "/v1/language/getList";


    public String getAllUrl(String path) {
        return host + path;
    }

}
