package com.qudian.idle.inspection.app.common.helper;


import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.qudian.idle.inspection.app.common.utils.json.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Named;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

@Component
public class MapStructHelper {

    @Named("toTimestamp")
    public Long toTimestamp(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return localDateTime.toInstant(ZoneOffset.UTC).toEpochMilli();
    }

    @Named("jsonToStrList")
    public List<String> jsonToStrList(String json) {
        if (JsonUtil.isEmptyList(json)) {
            return Lists.newArrayList();
        }
        return JsonUtil.parseArray(json, String.class, false);
    }

    @Named("strToSingleList")
    public List<String> strToSingleList(String str) {
        if (StringUtils.isEmpty(str)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(str);
    }


    @Named("strListToArray")
    public String[] toArray(List<String> stringList) {
        if (CollectionUtil.isEmpty(stringList)) {
            return new String[]{};
        }
        return stringList.toArray(new String[0]);
    }
}
