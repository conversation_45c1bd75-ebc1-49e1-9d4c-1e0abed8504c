package com.qudian.idle.inspection.app.common.enums;

import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.temporal.TemporalAdjusters;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <p>文件名称:com.qudian.toy.app.api.enums</p>
 * <p>文件描述:</p>
 * <p>版权所有: 版权所有(C)2019-2099</p>
 * <p>公 司: 趣店 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">kangjun</a>
 * @version 1.0
 * @since 2024/6/27 16:43
 */
public enum TimeZoneEnum {
    ACT("Australia/Darwin", "澳洲/达尔文"),
    AET("Australia/Sydney", "澳洲/悉尼"),
    AGT("America/Argentina/Buenos_Aires", "美洲/阿根廷/布宜诺斯艾利斯"),
    ART("Africa/Cairo", "非洲/开罗"),
    AST("America/Anchorage", "美洲/安克雷奇"),
    AT("America/Toronto", "美洲/多伦多"),
    BET("America/Sao_Paulo", "美洲/圣保罗"),
    BST("Asia/Dhaka", "亚洲/达卡"),
    CAT("Africa/Harare", "非洲/哈拉雷"),
    CNT("America/St_Johns", "美洲/圣约翰"),
    CST("America/Chicago", "美洲/芝加哥"),
    CTT("Asia/Shanghai", "亚洲/上海"),
    EAT("Africa/Addis_Ababa", "非洲/亚的斯亚贝巴"),
    ECT("Europe/Paris", "欧洲/巴黎"),
    IET("America/Indiana/Indianapolis", "美洲/印第安纳州/印第安纳波利斯"),
    IST("Asia/Kolkata", "亚洲/加尔各答"),
    JST("Asia/Tokyo", "亚洲/东京"),
    MIT("Pacific/Apia", "太平洋/阿皮亚"),
    NET("Asia/Yerevan", "亚洲/埃里温"),
    NST("Pacific/Auckland", "太平洋/奥克兰"),
    PLT("Asia/Karachi", "亚洲/卡拉奇"),
    PNT("America/Phoenix", "美洲/凤凰城"),
    PRT("America/Puerto_Rico", "美洲/波多黎各"),
    PST("America/Los_Angeles", "美洲/洛杉矶"),
    SST("Pacific/Guadalcanal", "太平洋/瓜达尔卡纳尔岛"),
    VST("Asia/Ho_Chi_Minh", "亚洲/胡志明市"),
    EST("-05:00", "东部标准时间"),
    MST("-07:00", "山地标准时间"),
    HST("-10:00", "夏威夷-阿留申标准时区"),
    EM("Europe/Madrid", "西班牙/马德里"),
    GMT("Europe/London", "英国/伦敦"),
    BJT("+08:00", "北京时间");

    private final String zoneIdName;
    private final String zoneIdNameCn;

    public String getZoneIdName() {
        return this.zoneIdName;
    }

    public String getZoneIdNameCn() {
        return this.zoneIdNameCn;
    }

    private TimeZoneEnum(String zoneIdName, String zoneIdNameCn) {
        this.zoneIdName = zoneIdName;
        this.zoneIdNameCn = zoneIdNameCn;
    }

    public static LocalDateTime getCustomizeLocalDateTime(LocalDateTime localDateTime, TimeZoneEnum timeZoneEnum) {
        ZoneId currentZone = getZone();
        ZoneId newZone = ZoneId.of(timeZoneEnum.getZoneIdName());
        return localDateTime.atZone(currentZone).withZoneSameInstant(newZone).toLocalDateTime();
    }

    public static LocalDateTime transDefaultLocalDateTime(Date date, ZoneId zoneId) {
        Instant instant = date.toInstant();
        ZoneId defaultZoneId = ZoneId.systemDefault();
        LocalDateTime dateTime = instant.atZone(defaultZoneId).toLocalDateTime();
        return dateTime.atZone(zoneId).withZoneSameInstant(defaultZoneId).toLocalDateTime();
    }

    public static ZoneOffset getZone() {
        return OffsetDateTime.now().getOffset();
    }

    public static List<LocalDate> getListLocalDateByStep(int startDayOfWeek, int endDayStep) {
        LocalDate receiveTimeLocal = LocalDate.now(ZoneId.of(AET.getZoneIdName()));
        DayOfWeek startEnum = DayOfWeek.of(startDayOfWeek);
        LocalDate monday = receiveTimeLocal.with(TemporalAdjusters.previousOrSame(startEnum));
        LocalDate nextThursday = monday.plusDays((long)endDayStep);
        return Arrays.asList(monday, nextThursday);
    }

    public static List<LocalDate> getListLocalDateByStep(int startDayOfWeek, int endDayStep, TimeZoneEnum timeZoneEnum) {
        LocalDate receiveTimeLocal = LocalDate.now(ZoneId.of(timeZoneEnum.getZoneIdName()));
        DayOfWeek startEnum = DayOfWeek.of(startDayOfWeek);
        LocalDate monday = receiveTimeLocal.with(TemporalAdjusters.previousOrSame(startEnum));
        LocalDate nextThursday = monday.plusDays((long)endDayStep);
        return Arrays.asList(monday, nextThursday);
    }

    public static Date LocalDateTimeToDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static ZoneId getZoneId(TimeZoneEnum timeZoneEnum) {
        return ZoneId.of(timeZoneEnum.getZoneIdName());
    }

    public static LocalDateTime dateToLocalDateTime(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public static Date transDefaultDate(Date date, ZoneId zoneId) {
        return LocalDateTimeToDate(transDefaultLocalDateTime(date, zoneId));
    }

    public static TimeZoneEnum transform(String zoneIdName) {
        return (TimeZoneEnum)Arrays.stream(values()).filter((timeZoneEnum) -> {
            return timeZoneEnum.getZoneIdName().equals(zoneIdName);
        }).findFirst().orElse((TimeZoneEnum) null);
    }
}
