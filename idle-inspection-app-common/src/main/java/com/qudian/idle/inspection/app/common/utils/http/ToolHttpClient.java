package com.qudian.idle.inspection.app.common.utils.http;

import com.alibaba.fastjson.JSON;
import com.qudian.lme.base.vo.BaseResponseVo;
import com.qudian.pdt.toolkit.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.HttpUrl;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

import java.io.IOException;
import java.util.Map;

/**
 * <p>文件名称:com.qudian.lme.driver.common.utils.common.ToolHttpClient</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2023/10/12
 */
@Slf4j
public class ToolHttpClient extends HttpClientBase {
    public ToolHttpClient(long connectionTimeout, long readTimeout, int MAX_IDLE_CONNECTIONS, long KEEP_ALIVE_DURATION_MINUTE, int MAX_REQUESTS) {
        super(connectionTimeout, readTimeout, MAX_IDLE_CONNECTIONS, KEEP_ALIVE_DURATION_MINUTE, MAX_REQUESTS);
    }

    public Response newCallHttp(Request request) throws IOException {
        return okHttpClient.newCall(request).execute();
    }

    public BaseResponseVo get(HttpUrl.Builder uriBuilder) throws IOException {
        Request request = new Request.Builder()
                .url(uriBuilder.build().toString())
                .get()
                .build();
        Response response = this.newCallHttp(request);
        if (!response.isSuccessful()) {
            throw new IOException("Unexpected code " + response);
        }
        if (org.apache.commons.lang3.ObjectUtils.isEmpty(response.body())) {
            throw new BizException("response body missing");
        }
        String responseBody = response.body().string();
        return JSON.parseObject(responseBody, BaseResponseVo.class);
    }

    public BaseResponseVo post(String url, String postJsonData) throws IOException {
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        Request request = new Request.Builder()
                .url(url)
                .post(RequestBody.create(mediaType, postJsonData))
                .build();
        Response response = this.newCallHttp(request);
        if (!response.isSuccessful()) {
            throw new IOException("Unexpected code " + response);
        }
        if (org.apache.commons.lang3.ObjectUtils.isEmpty(response.body())) {
            throw new BizException("response body missing");
        }
        String responseBody = response.body().string();
        return JSON.parseObject(responseBody, BaseResponseVo.class);
    }

    public BaseResponseVo getWithHeaders(HttpUrl.Builder uriBuilder, Map<String, String> headers) throws IOException {
        Request request = new Request.Builder()
            .url(uriBuilder.build().toString())
            .headers(Headers.of(headers))
            .get()
            .build();
        Response response = this.newCallHttp(request);
        if (!response.isSuccessful()) {
            throw new IOException("Unexpected code " + response);
        }
        if (org.apache.commons.lang3.ObjectUtils.isEmpty(response.body())) {
            throw new BizException("response body missing");
        }
        String responseBody = response.body().string();
        return JSON.parseObject(responseBody, BaseResponseVo.class);
    }

}
