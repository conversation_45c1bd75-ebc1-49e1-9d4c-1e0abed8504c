package com.qudian.idle.inspection.app.common.config;


import com.qudian.idle.inspection.app.common.utils.http.ToolHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @desc: httpClient
 * @date: Created at 2020-11-02 15:19
 */
@Configuration
public class ToolHttpClientConfig {
    //TODO@ch 在properties中配置
    private static final long CONNECTION_TIMEOUT = 5_000;
    private static final long READ_TIMEOUT = 60_000;

    private static final int MAX_IDLE_CONNECTIONS = 200;
    private static final long KEEP_ALIVE_DURATION_MINUTE = 3;

    private static final int MAX_REQUESTS = 140;

    @Bean
    public ToolHttpClient toolHttpClient() {
        return new ToolHttpClient(CONNECTION_TIMEOUT, READ_TIMEOUT, MAX_IDLE_CONNECTIONS
                , KEEP_ALIVE_DURATION_MINUTE, MAX_REQUESTS);
    }

}
