package com.qudian.idle.inspection.app.common.utils.common;

import com.qudian.idle.inspection.app.common.enums.TimeZoneEnum;
import org.apache.dubbo.rpc.RpcContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <p>文件名称:com.qudian.toy.app.common.utils.common</p>
 * <p>文件描述:</p>
 * <p>版权所有: 版权所有(C)2019-2099</p>
 * <p>公 司: 趣店 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">kangjun</a>
 * @version 1.0
 * @since 2024/6/27 16:57
 */
public class TimeZoneUtil {
    private static final Logger logger = LoggerFactory.getLogger(TimeZoneUtil.class);

    public TimeZoneUtil() {
    }

    public static TimeZoneEnum getRpcTimeZone() throws RuntimeException {
        String timeZone = RpcContext.getContext().getAttachment("timezone");
        return TimeZoneEnum.transform(timeZone);
    }
}
