package com.qudian.idle.inspection.app.common.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
@Getter
public class SmsConfig {

    public static final String SMS_KEY_FREQUENCY = "_FREQUENCY";

    public static final String SMS_KEY_LIMIT = "_LIMIT";

    @Value("#{'${sms.audit.mobiles:}'.split(',')}")
    private List<String> auditMobileList;

    @Value("${sms.audit.defaultCode:123456}")
    private String auditDefaultCode;

    @Value("${sms.business:idle}")
    private String business;

    @Value("${sms.limit.count:50}")
    private Long smsLimitCount;

    public boolean isAuditMobile(String mobile) {
        return auditMobileList.contains(mobile);
    }


}
