package com.qudian.idle.inspection.app.common.utils.json;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Slf4j
public class JsonUtil {

    public static final String EMPTY_LIST_STR = "[]";

    public static final String EMPTY_OBJECT_STR = "{}";


    public static boolean isEmptyList(String jsonStr) {
        return StringUtils.isBlank(jsonStr) || EMPTY_LIST_STR.equals(jsonStr);
    }

    public static boolean isEmptyObject(String jsonStr) {
        return StringUtils.isBlank(jsonStr) || EMPTY_OBJECT_STR.equals(jsonStr);
    }

    /**
     * 以json格式字符串返回
     *
     * @param source
     * @return
     */
    public static String toJsonString(Object source) {
        if (source == null) {
            return null;
        }
        return JsonUtil.toJsonString(source, false);
    }

    public static String toJsonStringRetainClass(Object source) {
        if (source == null) {
            return null;
        }
        return JsonUtil.toJsonStringRetainClass(source, false);
    }

    /**
     * 以json格式字符串返回
     *
     * @param source
     * @param isThrowException
     * @return
     */
    public static String toJsonString(Object source, boolean isThrowException) {
        try {
            return JSON.toJSONString(source, SerializerFeature.DisableCircularReferenceDetect);
        } catch (Exception e) {
            if (isThrowException) {
                throw new RuntimeException(e);
            }
            log.error("object to Json String error, object:{}", source);
        }
        return null;
    }

    /**
     * 以json格式字符串返回
     *
     * @param source
     * @param isThrowException
     * @return
     */
    public static String toJsonStringRetainClass(Object source, boolean isThrowException) {
        try {
            return JSON.toJSONString(source, SerializerFeature.WriteClassName);
        } catch (Exception e) {
            if (isThrowException) {
                throw new RuntimeException(e);
            }
            log.error("object to Json String error, object:{}", source);
        }
        return null;
    }

    /**
     * 解析 json 字符串
     *
     * @param jsonStr
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> T parseObject(String jsonStr, Class<T> clazz) {
        return JsonUtil.parseObject(jsonStr, clazz, true);
    }

    /**
     * 解析 json 字符串
     *
     * @param jsonStr
     * @param clazz
     * @param isThrowException 是否抛出异常
     * @param <T>
     * @return
     */
    public static <T> T parseObject(String jsonStr, Class<T> clazz, boolean isThrowException) {
        if (JsonUtil.isEmptyObject(jsonStr)) {
            return null;
        }
        try {
            return JSON.parseObject(jsonStr, clazz);
        } catch (Exception e) {
            if (isThrowException) {
                throw new RuntimeException(e);
            }
            log.error("json string to object error, json string:{}", jsonStr);
        }
        return null;
    }

    public static <T> List<T> parseArray(String jsonStr, Class<T> clazz, boolean isThrowException) {
        if (StringUtils.isBlank(jsonStr)) {
            return Lists.newArrayList();
        }
        try {
            return JSON.parseArray(jsonStr, clazz);
        } catch (Exception e) {
            if (isThrowException) {
                throw new RuntimeException(e);
            }
            log.error("json string to array object error, json string:{}", jsonStr);
        }
        return null;
    }

    public static <T> List<T> parseArray(String jsonStr, Class<T> clazz) {
        return parseArray(jsonStr, clazz, true);
    }


    public static <VALUE> Map<String,VALUE> parseStringMap(String jsonStr, Class<VALUE> valueClass) {
        JSONObject jsonObject = JSON.parseObject(jsonStr);
        Set<Map.Entry<String, Object>> entrySet = jsonObject.entrySet();
        Map<String, VALUE> result = Maps.newHashMap();
        for (Map.Entry<String, Object> entry : entrySet) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (value instanceof JSONObject) {
                JSONObject jsonObjectValue = (JSONObject) value;
                result.put(key, jsonObjectValue.toJavaObject(valueClass));
            } else {
                log.error("parseMap失败,value非JsonObject");
                continue;
            }
        }
        return result;
    }

    public static String getUniqueIdentification(Object obj) {
        String jsonStr = JSON.toJSONString(obj);
        // 转换为有序 json 对象
        JSONObject jsonObject = JSON.parseObject(jsonStr, Feature.OrderedField);
        return String.valueOf(jsonObject.hashCode());
    }

    public static boolean equals(Object source01, Object source02) {
        if (source01 == null || source02 == null) {
            log.info("source01 is null?:{}, source02 is null?:{},return false!", source01 == null, source02 == null);
            return false;
        }
        String sourceStr01 = toJsonString(source01);
        String sourceStr02 = toJsonString(source02);
        return Objects.equals(sourceStr01, sourceStr02);
    }

    public static <T> T objToTargetClass(Object respData, Class<T> onlineDeviceStatusRespDTOClass) {
        if (respData == null) {
            return null;
        }

        if (onlineDeviceStatusRespDTOClass.isAssignableFrom(respData.getClass())) {
            return (T) respData;
        }
        return parseObject(toJsonString(respData), onlineDeviceStatusRespDTOClass);
    }
}
