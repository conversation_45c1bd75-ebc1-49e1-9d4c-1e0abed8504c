package com.qudian.idle.inspection.app.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BizErrorEnum {

    SUCCESS(0, "success"),
    FAIL(-1, "fail"),
    PROCESSING(2, "processing"),

    // 通用异常 1001-1999
    UNKNOWN(1001, "unknown anomaly"),
    SYSTEM_ERR(1002, "ERROR-system error","system.error"),
    TIME_OUT(1003, "the request timed out"),
    REPEAT(1004, "repeated calls"),
    PARAMETER_VERIFICATION(1005, "the parameter is abnormal","parameter.is.abnormal"),
    NOT_SUPPORT_OPERATE(1006, "not support operate", "not.support.operate"),
    UNAUTHORIZED_ACCESS(1007, "unauthorized access", "unauthorized.access"),

    // 业务异常 2001-2999
    USER_MOBILE_FORMAT_INCORRECT(2001, "The format of the phone number is abnormal", "idle.mobile.format.incorrect"),
    USER_MOBILE_EXIST(2002, "The phone number already exists", "idle.mobile.exist"),
    USER_NOT_EXIST(2003, "The user does not exist", "idle.user.not.exist"),
    NOT_SUPPORT_JOB_LEVEL(2004, "The job level is not supported", "idle.job.level.not.supported"),
    MOBILE_NOT_SIGNING(2005, "The mobile number is not signed", "idle.mobile.not.signing"),
    SEND_MSG_TYPE_ERROR(2006, "无法识别消息来源类型", "idle.msg.send.type.error"),
    HIGH_QUANTITY_SMS_REQUEST(2007, "短信发送频率过高，请稍后重试", "idle.msg.send.high.quantity.error"),
    LIMIT_QUANTITY_SMS_REQUEST(2008, "验证码发送次数达到上限", "idle.msg.send.limit.quantity.error"),
    SMS_VERIFICATION_CODE(2009, "验证码错误", "idle.msg.send.code.error"),

    // 内部运行异常 3001-3999
    JOB_EXECUTING(3001, "The task is running abnormally"),
    DB_BUSY(3002, "The database operation is abnormal, please try again later"),


    // 外部调用异常 4001-4999
    RPC_INVOKE(4001, "The RPC is invoked abnormally"),
    THIRD_SYSTEM_ERROR(4002, "Third-party system errors"),
    SECOND_PARTY_ERROR(4003, "Calling a two-party library error"),
    CHECK_PHONE_ERROR(4008, "The format of the verification phone number is abnormal","idle.mobile.format.incorrect"),
    CHECK_EMAIL_ERROR(4009, "The format of the verification mailbox is abnormal"),
    FILE_SIGNED_REQUEST_ERROR(4010,"File signed invoked error"),


    //Auth 5001-5999
    JWT_PARAM_VERIFY_ERROR(5001, "The parameter is incorrect"),
    JWT_UNKNOWN_PLATFORM(5002, "Unknown platform"),
    JWT_VERIFY_FAIL(5003, "Authentication failed"),
    KICK_ALL_ERROR(5004, "The account failed to go offline on all platforms"),;


    BizErrorEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
        this.i18nKey = "";
    }

    private Integer code;
    private String msg;
    private String i18nKey;
}
