package com.qudian.idle.inspection.app.common.enums;

import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum SmsOperatorNameEnum {

    INSPECTION("INSPECTION","用户");

    /**
     * 编码
     */
    private String code;

    /**
     * 描述
     */
    private String desc;

    /**
     * 根据 code 判断是否是该枚举
     *
     * @param code code
     * @return true:是，false:否
     */
    public boolean same(String code) {
        return Objects.equals(this.code, code);
    }

    /**
     * 根据 code 获取对应枚举值
     *
     * @param code code
     * @return 对应枚举，匹配不到返回null
     */
    public static SmsOperatorNameEnum getByCode(String code) {
        for (SmsOperatorNameEnum value : values()) {
            if (value.same(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据 code 获取对应枚举值
     *
     * @param code        code
     * @param defaultEnum 默认值
     * @return 对应枚举，匹配不到返回 defaultEnum
     */
    public static SmsOperatorNameEnum getByCode(String code, SmsOperatorNameEnum defaultEnum) {
        SmsOperatorNameEnum result = getByCode(code);
        return result != null ? result : defaultEnum;
    }
}
