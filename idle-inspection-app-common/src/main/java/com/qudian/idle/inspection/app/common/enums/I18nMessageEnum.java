package com.qudian.idle.inspection.app.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;


@Getter
@AllArgsConstructor
public enum I18nMessageEnum {

    LOGIN_SMS_CONTENT("idle.sms.content","短信验证码","验证码：%s。您正在使用验证码登录，15分钟内有效，切勿将验证码泄露于他人。"),

    ;

    /**
     * 编码
     */
    private String code;

    /**
     * 描述
     */
    private String desc;

    /**
     * 默认内容
     */
    private String defaultContent;
    
    /**
     * 根据 code 判断是否是该枚举
     * @param code code
     * @return true:是，false:否
     */
    public boolean same(String code) {
        return Objects.equals(this.code, code);
    }

    /**
     * 根据 code 获取对应枚举值
     * @param code code
     * @return 对应枚举，匹配不到返回null
     */
    public static I18nMessageEnum getByCode(String code) {
        for (I18nMessageEnum value : values()) {
            if (value.same(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据 code 获取对应枚举值
     * @param code code
     * @param defaultEnum 默认值
     * @return 对应枚举，匹配不到返回 defaultEnum
     */
    public static I18nMessageEnum getByCode(String code,I18nMessageEnum defaultEnum) {
        I18nMessageEnum result = getByCode(code);
        return result != null ? result : defaultEnum;
    }
}
