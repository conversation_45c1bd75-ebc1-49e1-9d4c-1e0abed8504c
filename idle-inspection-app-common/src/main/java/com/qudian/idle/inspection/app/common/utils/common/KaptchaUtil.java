package com.qudian.idle.inspection.app.common.utils.common;

import com.google.code.kaptcha.impl.DefaultKaptcha;
import com.qudian.idle.inspection.app.common.utils.middleware.RedisUtil;
import com.qudian.pdt.toolkit.common.enums.ExceptionEnum;
import com.qudian.pdt.toolkit.common.exception.BizException;
import com.qudian.pdt.toolkit.common.util.CommonUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <p>文件名称:com.qudian.lme.driver.common.utils.common.KaptchaUtil</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2022/11/10
 */
@Component
@Slf4j
public class KaptchaUtil {
    @Resource
    private DefaultKaptcha defaultKaptcha;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private CommonUtil commonUtil;

    private static final String DEFAULT_SMS_CODE = "000000";

    /**
     * 生成文本验证码
     *
     * @param redisKey
     * @return
     */
    public String createTextCode(String redisKey) {
        String textCode = null;
        try {
            textCode = defaultKaptcha.createText();
            cacheTextCode(redisKey, textCode);
        } catch (Exception e) {
            log.warn("kaptcha生成验证码失败", e);
            throw new BizException(ExceptionEnum.SYSTEM_ERR, "验证码生成失败");
        }
        return textCode;
    }

    public String createTextCode(String redisKey, int seconds) {
        String textCode = null;
        try {
            textCode = defaultKaptcha.createText();
            cacheTextCode(redisKey, textCode, seconds);
        } catch (Exception e) {
            log.warn("kaptcha生成验证码失败", e);
            throw new BizException(ExceptionEnum.SYSTEM_ERR, "验证码生成失败");
        }
        return textCode;
    }

    public void cacheTextCode(String redisKey, String textCode) {
        try {
            /* 生产验证码字符串 */
            log.info("redis key={},sms code={}", redisKey, textCode);
            /* 存Redis，有效期15分钟 */
            redisUtil.setex(redisKey, textCode, 15 * 60);
        } catch (Exception e) {
            log.warn("kaptcha生成验证码失败", e);
            throw new BizException(ExceptionEnum.SYSTEM_ERR, "验证码生成失败");
        }
    }

    public void cacheTextCode(String redisKey, String textCode, int seconds) {
        try {
            /* 生产验证码字符串 */
            log.info("redis key={},sms code={}", redisKey, textCode);
            /* 存Redis，有效期 seconds/60 分钟 */
            redisUtil.setex(redisKey, textCode, seconds);
        } catch (Exception e) {
            log.warn("kaptcha生成验证码失败", e);
            throw new BizException(ExceptionEnum.SYSTEM_ERR, "验证码生成失败");
        }
    }

    /**
     * 验证码校验
     *
     * @param redisKey
     * @param inputCode
     * @return
     */
    public boolean canBeVerified(String redisKey, String inputCode) {
        /* 参数校验 */
        if (StringUtils.isBlank(redisKey)) {
            throw new BizException(ExceptionEnum.PARAMETER_VERIFICATION);
        }
        /* 测试环境留个口 */
        if (commonUtil.isTest() && DEFAULT_SMS_CODE.equals(inputCode)) {
            return true;
        }
        /* 本地或联调环境无需验证 */
        if (commonUtil.isLocalOrDev()) {
            return true;
        }
        /* 拼装redis key，获取value */
        log.info("checkMobileVerificationCode canBeVerified start");
        String correctCode = redisUtil.get(redisKey, 0);
        log.info("checkMobileVerificationCode canBeVerified end");
        log.info("[登录验证码校验]：correctCode=" + correctCode + "，inputCode=" + inputCode + ", key=" + redisKey);
        if (!inputCode.equals(correctCode)) {
            log.info("错误的验证码");
            return false;
        }
        /* 验证成功，删除redis缓存 */
        redisUtil.del(0, redisKey);
        return true;
    }


}
