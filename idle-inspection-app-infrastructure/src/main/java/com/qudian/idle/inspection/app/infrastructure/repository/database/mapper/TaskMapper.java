package com.qudian.idle.inspection.app.infrastructure.repository.database.mapper;

import com.qudian.idle.inspection.app.api.vo.request.task.TaskListQueryReqVO;
import com.qudian.idle.inspection.app.api.vo.response.auth.InspectionPermissionQueryRespVO;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.TaskPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务主表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-13 17:02:13
 */
@Mapper
public interface TaskMapper extends BaseMapper<TaskPO> {

    @Select({
            "<script>",
            "SELECT DISTINCT t.*",
            "FROM task t",
            "JOIN task_sub ts ON ts.task_no = t.task_no",
            "JOIN task_pool tp",
            "  ON tp.delete_flag = 0",
            " AND tp.task_no = ts.task_no",
            " AND (tp.task_sub_no IS NULL OR tp.task_sub_no = ts.id)",
            "WHERE t.delete_flag = 0",
            "  AND ts.delete_flag = 0",
            "  AND ts.status = 0",
            "  <if test='taskCategoryId != null'>",
            "    AND t.task_category_id = #{taskCategoryId}",
            "  </if>",
            "  <if test='productBrandId != null and productBrandId != \"\"'>",
            "    AND t.product_brand_id = #{productBrandId}",
            "  </if>",
            "  <if test='productCategoryId != null and productCategoryId != \"\"'>",
            "    AND t.product_category_id = #{productCategoryId}",
            "  </if>",
            "  <if test='deadlineStartTime != null'>",
            "    AND ts.deadline_time &gt;= #{deadlineStartTime}",
            "  </if>",
            "  <if test='deadlineEndTime != null'>",
            "    AND ts.deadline_time &lt;= #{deadlineEndTime}",
            "  </if>",
            "  <if test='jobPermissionList != null and jobPermissionList.size &gt; 0'>",
            "    AND (tp.job_id, tp.job_level) IN (",
            "      <foreach collection='jobPermissionList' item='jp' separator=','>",
            "        (#{jp.jobId}, #{jp.jobLevel})",
            "      </foreach>",
            "    )",
            "  </if>",
            "ORDER BY ts.created_time DESC",
            "</script>"
    })
    List<TaskPO> selectByTaskTypeAndJobLevel(TaskListQueryReqVO query);


    @Select({
            "<script>",
            "SELECT ts.id",
            "FROM task_sub ts",
            "JOIN task_pool tp",
            "  ON tp.delete_flag = 0",
            " AND tp.task_no = ts.task_no",
            " AND (tp.task_sub_no IS NULL OR tp.task_sub_no = ts.id)",
            "WHERE ts.delete_flag = 0",
            "  AND ts.status = 0",
            "  AND ts.task_no = #{taskNo}",
            "  AND (tp.job_id, tp.job_level) IN (",
            "    <foreach collection='jobPermissionList' item='jp' separator=','>",
            "      (#{jp.jobId}, #{jp.jobLevel})",
            "    </foreach>",
            "  )",
            "ORDER BY ts.created_time ASC",
            "LIMIT 1",
            "</script>"
    })
    Long selectClaimableSubIdByTaskNo(
            @Param("taskNo") String taskNo,
            @Param("jobPermissionList") List<InspectionPermissionQueryRespVO.JobPermission> jobPermissionList
    );

    @Update({
            "UPDATE task_sub",
            "SET status = 1,",
            "    deadline_time = #{deadlineTime}",
            "WHERE id = #{taskSubId}",
            "  AND delete_flag = 0",
            "  AND status = 0"
    })
    int claimTaskSub(@Param("taskSubId") Long taskSubId,
                     @Param("userId") Long userId,
                     @Param("deadlineTime") LocalDateTime deadlineTime);

}
