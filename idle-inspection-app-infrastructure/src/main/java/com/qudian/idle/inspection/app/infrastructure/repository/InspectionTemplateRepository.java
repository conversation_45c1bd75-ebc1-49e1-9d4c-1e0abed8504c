package com.qudian.idle.inspection.app.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Repository;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qudian.idle.inspection.app.api.enums.DeleteFlagEnum;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.InspectionTemplatePO;
import com.qudian.idle.inspection.app.infrastructure.repository.database.mapper.InspectionTemplateMapper;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-14 14:58:54
 */
@Repository
@Slf4j
public class InspectionTemplateRepository extends ServiceImpl<InspectionTemplateMapper, InspectionTemplatePO>{


    @Resource
    private InspectionTemplateMapper inspectionTemplateMapper;

    private LambdaQueryWrapper<InspectionTemplatePO> getQueryWrapper() {
        LambdaQueryWrapper<InspectionTemplatePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InspectionTemplatePO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return queryWrapper;
    }


    private LambdaUpdateWrapper<InspectionTemplatePO> getUpdateWrapper() {
        LambdaUpdateWrapper<InspectionTemplatePO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(InspectionTemplatePO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return updateWrapper;
    }

}