package com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.internal.tool;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PhoneCheckQuery implements Serializable {

    /**
     * 电话
     */
    private String phone;

    /**
     * 国家码
     */
    private String countryCode;

}
