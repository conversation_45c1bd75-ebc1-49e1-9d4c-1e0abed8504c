package com.qudian.idle.inspection.app.infrastructure.repository.database.po.certificate;

import com.baomidou.mybatisplus.annotation.TableName;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>文件名称:com.qudian.idle.inspection.app.infrastructure.repository.database.po.Certificates</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/21
 */
@Data
@TableName("certificate")
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CertificatePO extends BasePO {
    private String certificateNo;  //鉴别证书编号
    private String antiCounterfeitNo;   //防伪扣编号
    private Integer verdict; //鉴定结果 1-未通过 2-通过
    private String sn;  //商品sn码
    private String orderNo;  //订单号
    private String skuId;   //SKU_ID
    private String goodsSnapshot;   //商品快照(JSON)
    private String orderSnapshot;   //订单快照(JSON)
    private Integer queryCnt;   //报告查询次数
    private LocalDateTime lastQueryTime;    //上次查询时间
}
