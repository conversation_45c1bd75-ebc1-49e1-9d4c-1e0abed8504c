package com.qudian.idle.inspection.app.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Repository;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qudian.idle.inspection.app.api.enums.DeleteFlagEnum;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.RelationJobTaskCategoryPO;
import com.qudian.idle.inspection.app.infrastructure.repository.database.mapper.RelationJobTaskCategoryMapper;

/**
 * 岗位-任务分类关联表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-13 17:02:13
 */
@Repository
@Slf4j
public class RelationJobTaskCategoryRepository extends ServiceImpl<RelationJobTaskCategoryMapper, RelationJobTaskCategoryPO>{


    @Resource
    private RelationJobTaskCategoryMapper relationJobTaskCategoryMapper;

    private LambdaQueryWrapper<RelationJobTaskCategoryPO> getQueryWrapper() {
        LambdaQueryWrapper<RelationJobTaskCategoryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RelationJobTaskCategoryPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return queryWrapper;
    }


    private LambdaUpdateWrapper<RelationJobTaskCategoryPO> getUpdateWrapper() {
        LambdaUpdateWrapper<RelationJobTaskCategoryPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RelationJobTaskCategoryPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return updateWrapper;
    }

}