package com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.internal.tool;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LanguageDetailResult {
    private Long id;
    private String locale;
    private String localeName;
    private String source;
    private String key;
    private String translation;
    private String createTime;
    private String updateTime;
}
