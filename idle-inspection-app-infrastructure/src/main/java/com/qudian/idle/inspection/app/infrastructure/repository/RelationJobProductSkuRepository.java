package com.qudian.idle.inspection.app.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Repository;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qudian.idle.inspection.app.api.enums.DeleteFlagEnum;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.RelationJobProductSkuPO;
import com.qudian.idle.inspection.app.infrastructure.repository.database.mapper.RelationJobProductSkuMapper;

/**
 * 岗位-商品sku关联表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-13 17:02:13
 */
@Repository
@Slf4j
public class RelationJobProductSkuRepository extends ServiceImpl<RelationJobProductSkuMapper, RelationJobProductSkuPO>{


    @Resource
    private RelationJobProductSkuMapper relationJobProductSkuMapper;

    private LambdaQueryWrapper<RelationJobProductSkuPO> getQueryWrapper() {
        LambdaQueryWrapper<RelationJobProductSkuPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RelationJobProductSkuPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return queryWrapper;
    }


    private LambdaUpdateWrapper<RelationJobProductSkuPO> getUpdateWrapper() {
        LambdaUpdateWrapper<RelationJobProductSkuPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RelationJobProductSkuPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return updateWrapper;
    }

}