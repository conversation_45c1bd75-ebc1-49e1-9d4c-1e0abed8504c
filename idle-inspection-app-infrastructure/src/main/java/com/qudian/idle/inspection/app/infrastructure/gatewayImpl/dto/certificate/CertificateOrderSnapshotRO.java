package com.qudian.idle.inspection.app.infrastructure.gatewayImpl.dto.certificate;

/**
 * <p>文件名称:com.qudian.idle.inspection.app.infrastructure.gatewayImpl.dto.certificate.CertificateGoodsSnapshotDTO</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/21
 */
public record CertificateOrderSnapshotRO(
        String amount,  //订单金额
        String orderTime,  //下单时间
        String orderSource,  //订单来源
        String purchaserName  //购买人昵称
) {
}
