package com.qudian.idle.inspection.app.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Repository;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qudian.idle.inspection.app.api.enums.DeleteFlagEnum;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.CustomizeInspectionTemplateRelationSkuPO;
import com.qudian.idle.inspection.app.infrastructure.repository.database.mapper.CustomizeInspectionTemplateRelationSkuMapper;

/**
 * 岗位-商品sku关联表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-14 14:58:53
 */
@Repository
@Slf4j
public class CustomizeInspectionTemplateRelationSkuRepository extends ServiceImpl<CustomizeInspectionTemplateRelationSkuMapper, CustomizeInspectionTemplateRelationSkuPO>{


    @Resource
    private CustomizeInspectionTemplateRelationSkuMapper customizeInspectionTemplateRelationSkuMapper;

    private LambdaQueryWrapper<CustomizeInspectionTemplateRelationSkuPO> getQueryWrapper() {
        LambdaQueryWrapper<CustomizeInspectionTemplateRelationSkuPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomizeInspectionTemplateRelationSkuPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return queryWrapper;
    }


    private LambdaUpdateWrapper<CustomizeInspectionTemplateRelationSkuPO> getUpdateWrapper() {
        LambdaUpdateWrapper<CustomizeInspectionTemplateRelationSkuPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CustomizeInspectionTemplateRelationSkuPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return updateWrapper;
    }

}