package com.qudian.idle.inspection.app.infrastructure.repository.database.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;


/**
 * 员工-岗位关联表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-13 16:45:45
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("relation_employee_job")
@NoArgsConstructor
@AllArgsConstructor
public class RelationEmployeeJobPO extends BasePO {
	private static final long serialVersionUID = 1L;

	/**
	 * 员工id
	 */
	private Long employeeId;
	/**
	 * 岗位id
	 */
	private String jobId;

	/**
	 * 等级
	 * @see com.qudian.idle.inspection.admin.api.enums.job.JobLevelEnum
	 */
	private Integer level;

}
