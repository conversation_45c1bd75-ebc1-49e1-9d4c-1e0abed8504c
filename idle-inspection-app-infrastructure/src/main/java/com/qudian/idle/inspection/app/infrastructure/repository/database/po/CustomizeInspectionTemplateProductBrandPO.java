package com.qudian.idle.inspection.app.infrastructure.repository.database.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.*;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;


/**
 * 岗位-商品品牌关联表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-14 14:58:54
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("customize_inspection_template_product_brand")
@NoArgsConstructor
@AllArgsConstructor
public class CustomizeInspectionTemplateProductBrandPO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;
	/**
	 * 自定义模板ID
	 */
	private Long customizeTemplateId;
	/**
	 * 品牌id
	 */
	private String productBrandId;
	/**
	 * 创建时间
	 */
	private LocalDateTime createdTime;
	/**
	 * 更新时间
	 */
	private LocalDateTime updatedTime;
	/**
	 * 创建人id
	 */
	private String creatorId;
	/**
	 * 最后编辑人id
	 */
	private String updaterId;
	/**
	 * 软删除标识，0表示未删除，1表示已删除
	 */
	private Integer deleteFlag;

}
