package com.qudian.idle.inspection.app.infrastructure.repository.database.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 子任务表
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "task_sub", autoResultMap = true)
@NoArgsConstructor
@AllArgsConstructor
public class TaskSubPO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 任务号
	 */
	private String taskNo;

	/**
	 * 子任务编号（如1-2、2-1）
	 */
	private String taskSubNo;

	/**
	 * 允许接任务的岗位级别
	 */
	private Integer jobLevel;

	/**
	 * 任务分类id，如1真假鉴定、2瑕疵鉴定、3成色鉴定、4拍照等
	 */
	private Long taskCategoryId;

	/**
	 * 执行人（认领人）id
	 */
	private Long userId;

	/**
	 * 执行人（认领人）
	 */
	private String userName;

	/**
	 * 任务状态 0未开始 1待取货 2进行中 3 完成 4
	 */
	private Integer status;

	/**
	 * 任务截止时间
	 */
	private LocalDateTime deadlineTime;

	/**
	 * 完成时间
	 */
	private LocalDateTime finishTime;

	/**
	 * 结果不同类型对应的枚举不同  1 2 3
	 */
	private Integer result;

	/**
	 * 结果说明
	 */
	private String resultDesc;

	/**
	 * 是否允许重复步骤：0不允许，1允许
	 */
	private Integer allowRepeat;

	/**
	 * 重复的步骤内容列表，结构同detail（JSON）
	 */
	private String repeatedDetails;

	/**
	 * 认领时间
	 */
	private LocalDateTime acceptTime;

	/**
	 * 0sku正确 1 sku错误
	 */
	private Integer skuCompResults;

	/**
	 * 创建时间
	 */
	private LocalDateTime createdTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updatedTime;

	/**
	 * 软删除标识，0表示未删除，非0表示已删除
	 */
	private Integer deleteFlag;
}