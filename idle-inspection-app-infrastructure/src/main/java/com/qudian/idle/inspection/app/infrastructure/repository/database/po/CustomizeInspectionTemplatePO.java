package com.qudian.idle.inspection.app.infrastructure.repository.database.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.*;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;


/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-14 14:58:54
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("customize_inspection_template")
@NoArgsConstructor
@AllArgsConstructor
public class CustomizeInspectionTemplatePO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;
	/**
	 * 模板名称
	 */
	private String name;
	/**
	 * 模板描述
	 */
	private String desc;
	/**
	 * 状态 1启用 0禁用
	 */
	private Integer status;
	/**
	 * 是否自定义瑕疵检测 1启用 0使用基础模板
	 */
	private Integer customizeDetection;
	/**
	 * 是否自定义真假 1启用 0使用基础模板
	 */
	private Integer customizeTrueOrFalse;
	/**
	 * 是否自定义成色 1启用 0使用基础模板
	 */
	private Integer customizeCondition;
	/**
	 * 是否自定义拍照留档 1启用 0使用基础模板
	 */
	private Integer customizePhoto;
	/**
	 * 创建人ID
	 */
	private Long creatorId;
	/**
	 * 创建人名称
	 */
	private String creatorName;
	/**
	 * 创建时间
	 */
	private LocalDateTime createdTime;
	/**
	 * 更新时间
	 */
	private LocalDateTime updatedTime;
	/**
	 * 软删除标识，0表示未删除，1表示已删除
	 */
	private Integer deleteFlag;

}
