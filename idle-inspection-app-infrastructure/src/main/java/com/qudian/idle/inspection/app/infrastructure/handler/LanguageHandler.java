package com.qudian.idle.inspection.app.infrastructure.handler;

import com.qudian.idle.inspection.app.common.enums.LanguageSourceEnum;
import com.qudian.idle.inspection.app.common.enums.LocaleEnum;
import com.qudian.idle.inspection.app.common.enums.RpcContextKeyEnum;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.internal.tool.LanguageDetailQuery;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.internal.tool.LanguageDetailResult;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.remote.ToolRemote;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Locale;
import java.util.Optional;

/**
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2024</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> board)
 * @version 1.0
 */
@Slf4j
@Component
@Configuration
public class LanguageHandler {
    @Resource
    private ToolRemote toolRemote;
    @Resource
    private I18nMessageHandler i18nMessageHandler;
    @Value("${default.language:zh-CN}")
    private String defaultLanguage;

    public String fetchFromMisRemote(String i18nCode, String defaultContent, String locale) {
        return this.fetchFromMisRemote(i18nCode, defaultContent, locale, true);
    }

    public String fetchFromMisRemoteException(String i18nCode, String defaultContent, String locale) {
        return this.fetchFromMisRemote(i18nCode, defaultContent, locale, false);
    }

    public String fetchByClientLanguage(String i18nCode, String defaultContent) {
        Locale locale = LocaleContextHolder.getLocale();
        String lang = locale.getLanguage() + "-" + locale.getCountry();
        return this.fetchFromMisRemote(i18nCode, defaultContent, lang, false);
    }

    public String fetchFromMisRemoteForSms(String i18nCode, String countryCode, String defaultContent) {
        LocaleEnum localeEnum = getLocaleEnum(countryCode);
        return this.fetchFromMisRemote(i18nCode, defaultContent, localeEnum.code, true);
    }

    public String fetchFromAppLanguage(String i18nCode, String defaultContent) {
        return fetchFromAppLanguage(i18nCode, defaultContent, null);
    }

    public String fetchFromAppLanguage(String i18nCode, String defaultContent, LocaleEnum lang) {
        if (lang == null) {
            lang = getLocaleByContext();
        }
        return fetchFromMisRemote(i18nCode, defaultContent, lang.code, true);
    }

    public String fetchFromDefaultLanguage(String i18nCode, String defaultContent) {
        LocaleEnum locale = getDefaultServiceLocale();
        return fetchFromMisRemote(i18nCode, defaultContent, locale.code, true);
    }

    @NotNull
    private static LocaleEnum getLocaleEnum(String countryCode) {
        LocaleEnum localeEnum = null;
        if ("ES".equalsIgnoreCase(countryCode)) {
            localeEnum = LocaleEnum.ES_ES;
        } else if ("CN".equalsIgnoreCase(countryCode)) {
            localeEnum = LocaleEnum.ZH_CN;
        } else {
            localeEnum = LocaleEnum.EN_US;
        }
        return localeEnum;
    }

    /**
     * 多语言翻译
     *
     * @param i18nCode
     * @param defaultContent
     * @param locale
     * @return
     */
    public String fetchFromMisRemote(String i18nCode, String defaultContent, String locale, boolean isBizMessage) {
        log.info("fetchFromMisRemote i18nCode {}, defaultContent {}, locale {}", i18nCode, defaultContent, locale);
        //ApplicationContextUtil.getBean(CommonUtil.class).isLocal()
        if (StringUtils.isBlank(i18nCode)) {
            log.info("fetchFromMisRemote i18nCode is blank, use default data:{}", defaultContent);
            return defaultContent;
        }

        //调用远程查询多语言翻译
        LanguageDetailQuery langReq = LanguageDetailQuery.builder().key(i18nCode).source(LanguageSourceEnum.LANGUAGE_SOURCE_NAME_IDLE_MIS.getCode()).locale(locale).build();
        List<LanguageDetailResult> languageDTOS = toolRemote.getLanguageList(langReq);
        log.info("fetchFromMisRemote i18nCode:{}, defaultContent:{}, locale:{}, languageDTOS:{}", i18nCode, defaultContent, locale, languageDTOS);
        if (CollectionUtils.isEmpty(languageDTOS) || null == languageDTOS.get(0) || StringUtils.isBlank(languageDTOS.get(0).getTranslation())) {
            log.warn("fetchFromMisRemote not configured i18nCode:{}, defaultContent:{}, locale:{}", i18nCode, defaultContent, locale);
        } else {
            log.info("fetchFromMisRemote use remote data:{}", languageDTOS.get(0).getTranslation());
            return languageDTOS.get(0).getTranslation();
        }

        //查询本地多语言进行翻译
        String localLanguage;
        if (isBizMessage) {
            localLanguage = i18nMessageHandler.generateBizMessage(i18nCode);
        } else {
            localLanguage = i18nMessageHandler.generateExceptionMessage(i18nCode);
        }
        log.info("fetchFromMisRemote localLanguage:{}", localLanguage);
        if (StringUtils.isNotBlank(localLanguage) && !localLanguage.equals(i18nCode)) {
            log.info("fetchFromMisRemote use local data:{}", localLanguage);
            return localLanguage;
        }

        //如果本地没有配置，返回默认值
        log.info("fetchFromMisRemote use default data:{}", defaultContent);
        return defaultContent;
    }

    /**
     * 获取服务器默认语言
     * @return
     */
    public LocaleEnum getDefaultServiceLocale() {
        LocaleEnum localeByCode = LocaleEnum.getLocaleByCode(defaultLanguage);
        return localeByCode;
    }

    public LocaleEnum getLocaleByRpcContext() {
        RpcContext rpcContext = RpcContext.getContext();
        String localeStr = Optional.ofNullable(rpcContext.getAttachment(RpcContextKeyEnum.LANG.getKey())).orElse("");
        if (StringUtils.isBlank(localeStr)) {
            log.info("从 RPC 上下文获取语言为空!localeStr:{}",localeStr);
            return null;
        }
        LocaleEnum localeEnum = LocaleEnum.getLocaleByCodeNotDefault(localeStr);
        if (localeEnum != null) {
            return localeEnum;
        }
        return null;
    }


    public LocaleEnum getLocaleByContext() {
        LocaleEnum localeEnum = getLocaleByRpcContext();
        if (localeEnum != null) {
            return localeEnum;
        }
        localeEnum = getDefaultServiceLocale();
        if (localeEnum != null) {
            return localeEnum;
        }
        return getLocaleByServiceContext();
    }

    public LocaleEnum getLocaleByServiceContext() {
        Locale locale = LocaleContextHolder.getLocale();
        String lang = locale.getLanguage() + "-" + locale.getCountry();
        LocaleEnum localeEnum = LocaleEnum.getLocaleByCodeNotDefault(lang);
        if (localeEnum != null) {
            return localeEnum;
        }
        log.info("从服务器上下文中获取语言为空!lang:{}",lang);
        return null;
    }

    public String fetchByLocalI18nMessage(String internationKey) {
        return i18nMessageHandler.generateBizMessage(internationKey);
    }



    /**
     * 本地多语言翻译
     */
    public String getLocalLanguage(String i18nCode) {
        LocaleEnum localeByContext = getLocaleByContext();

        String localLanguage;
        localLanguage = i18nMessageHandler.generateBizMessage(i18nCode,localeByContext);
        if (StringUtils.isNotBlank(localLanguage)) {
            //    log.info("fetchFromMisRemote use local data:{}", localLanguage);
            return localLanguage;
        }
        return i18nCode;

    }
}
