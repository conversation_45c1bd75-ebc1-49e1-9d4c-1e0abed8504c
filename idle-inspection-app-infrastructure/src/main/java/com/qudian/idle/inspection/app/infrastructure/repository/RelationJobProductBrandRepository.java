package com.qudian.idle.inspection.app.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Repository;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qudian.idle.inspection.app.api.enums.DeleteFlagEnum;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.RelationJobProductBrandPO;
import com.qudian.idle.inspection.app.infrastructure.repository.database.mapper.RelationJobProductBrandMapper;

/**
 * 岗位-商品品牌关联表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-13 17:02:13
 */
@Repository
@Slf4j
public class RelationJobProductBrandRepository extends ServiceImpl<RelationJobProductBrandMapper, RelationJobProductBrandPO>{


    @Resource
    private RelationJobProductBrandMapper relationJobProductBrandMapper;

    private LambdaQueryWrapper<RelationJobProductBrandPO> getQueryWrapper() {
        LambdaQueryWrapper<RelationJobProductBrandPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RelationJobProductBrandPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return queryWrapper;
    }


    private LambdaUpdateWrapper<RelationJobProductBrandPO> getUpdateWrapper() {
        LambdaUpdateWrapper<RelationJobProductBrandPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RelationJobProductBrandPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return updateWrapper;
    }

}