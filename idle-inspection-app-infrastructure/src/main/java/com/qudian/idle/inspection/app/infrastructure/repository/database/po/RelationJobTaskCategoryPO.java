package com.qudian.idle.inspection.app.infrastructure.repository.database.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;


/**
 * 岗位-任务分类关联表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-13 16:45:45
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("relation_job_task_category")
@NoArgsConstructor
@AllArgsConstructor
public class RelationJobTaskCategoryPO extends BasePO {

	/**
	 * 岗位id
	 */
	private Long jobId;
	/**
	 * 任务分类id
	 */
	private Long taskCategoryId;


}
