package com.qudian.idle.inspection.app.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qudian.idle.inspection.app.api.enums.DeleteFlagEnum;
import com.qudian.idle.inspection.app.infrastructure.repository.database.mapper.TaskSubRelationSkuMapper;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.TaskSubRelationSkuPO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 子任务关联的sku校对 仓储
 */
@Repository
@Slf4j
public class TaskSubRelationSkuRepository extends ServiceImpl<TaskSubRelationSkuMapper, TaskSubRelationSkuPO> {

    @Resource
    private TaskSubRelationSkuMapper taskSubRelationSkuMapper;

    private LambdaQueryWrapper<TaskSubRelationSkuPO> getQueryWrapper() {
        LambdaQueryWrapper<TaskSubRelationSkuPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskSubRelationSkuPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return queryWrapper;
    }

    private LambdaUpdateWrapper<TaskSubRelationSkuPO> getUpdateWrapper() {
        LambdaUpdateWrapper<TaskSubRelationSkuPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TaskSubRelationSkuPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return updateWrapper;
    }

    /**
     * 按子任务号查询
     */
    public List<TaskSubRelationSkuPO> listByTaskSubNo(String taskSubNo) {
        return this.list(getQueryWrapper().eq(TaskSubRelationSkuPO::getTaskSubNo, taskSubNo));
    }
}