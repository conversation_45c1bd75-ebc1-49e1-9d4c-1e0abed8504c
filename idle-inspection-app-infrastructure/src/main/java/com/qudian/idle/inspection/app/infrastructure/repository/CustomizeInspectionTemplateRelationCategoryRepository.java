package com.qudian.idle.inspection.app.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Repository;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qudian.idle.inspection.app.api.enums.DeleteFlagEnum;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.CustomizeInspectionTemplateRelationCategoryPO;
import com.qudian.idle.inspection.app.infrastructure.repository.database.mapper.CustomizeInspectionTemplateRelationCategoryMapper;

/**
 * 岗位-商品分类关联表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-14 14:58:53
 */
@Repository
@Slf4j
public class CustomizeInspectionTemplateRelationCategoryRepository extends ServiceImpl<CustomizeInspectionTemplateRelationCategoryMapper, CustomizeInspectionTemplateRelationCategoryPO>{


    @Resource
    private CustomizeInspectionTemplateRelationCategoryMapper customizeInspectionTemplateRelationCategoryMapper;

    private LambdaQueryWrapper<CustomizeInspectionTemplateRelationCategoryPO> getQueryWrapper() {
        LambdaQueryWrapper<CustomizeInspectionTemplateRelationCategoryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomizeInspectionTemplateRelationCategoryPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return queryWrapper;
    }


    private LambdaUpdateWrapper<CustomizeInspectionTemplateRelationCategoryPO> getUpdateWrapper() {
        LambdaUpdateWrapper<CustomizeInspectionTemplateRelationCategoryPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CustomizeInspectionTemplateRelationCategoryPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return updateWrapper;
    }

}