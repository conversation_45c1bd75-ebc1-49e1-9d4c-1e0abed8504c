package com.qudian.idle.inspection.app.infrastructure.handler;

import com.qudian.idle.inspection.app.common.enums.LocaleEnum;
import com.qudian.idle.inspection.app.common.utils.json.JsonUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Locale;
import java.util.Map;

/**
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2024</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> board)
 * @version 1.0
 */
@Component
@Slf4j
public class I18nMessageHandler {

    @Resource
    @Lazy
    private Map<String, MessageSource> sourceMapper;

    @Resource(name = "bizI18nMessage")
    @Lazy
    private MessageSource bizI18nMessage;

    @Value("${default.language:zh-CN}")
    private String lang;

    public String generateBizMessage(String i18nCode, Object... args) {
        Locale systemLocale = getSystemLocale();
        return sourceMapper.getOrDefault("bizI18nMessage", bizI18nMessage).getMessage(i18nCode, args,systemLocale );
    }

    public String generateExceptionMessage(String i18nCode, Object... args) {
        return sourceMapper.getOrDefault("exceptionI18nMessage", bizI18nMessage).getMessage(i18nCode, args, getSystemLocale());
    }

    public String generateBizMessage(String i18nCode, LocaleEnum localeEnum, Object... args) {
        Locale locale = getLocaleFromEnum(localeEnum);
        return sourceMapper.getOrDefault("bizI18nMessage", bizI18nMessage).getMessage(i18nCode, args, locale);
    }

    public LocaleEnum getSystemLocaleEnum() {
        LocaleEnum localeEnum = LocaleEnum.getLocaleByCodeNotDefault(lang);
        return localeEnum;
    }

    public Locale getSystemLocale() {
        LocaleEnum localeEnum = getSystemLocaleEnum();
        return getLocaleFromEnum(localeEnum);

    }

    public Locale getLocaleFromEnum(LocaleEnum localeEnum) {
        if (localeEnum == null) {
            log.error("LocaleEnum为空，默认使用英语!");
            return Locale.US;
        }

        switch (localeEnum) {
            case ZH_CN:
                return Locale.CHINA;
            case EN_US:
                return Locale.US;
            default:
                log.error("根据:localeEnum{}读取不到对应的语言,默认为英语!", JsonUtil.toJsonString(localeEnum));
                return Locale.US;
        }
    }
}
