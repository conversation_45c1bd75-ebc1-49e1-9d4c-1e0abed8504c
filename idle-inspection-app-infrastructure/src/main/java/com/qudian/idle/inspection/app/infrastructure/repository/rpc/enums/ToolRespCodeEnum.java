package com.qudian.idle.inspection.app.infrastructure.repository.rpc.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;


@Getter
@AllArgsConstructor
public enum ToolRespCodeEnum {

    SUCCESS(200,"成功");

    /**
     * 编码
     */
    private int code;

    /**
     * 描述
     */
    private String desc;

    /**
     * 根据 code 判断是否是该枚举
     *
     * @param code code
     * @return true:是，false:否
     */
    public boolean same(Integer code) {
        return Objects.equals(this.code, code);
    }

    /**
     * 根据 code 获取对应枚举值
     *
     * @param code code
     * @return 对应枚举，匹配不到返回null
     */
    public static ToolRespCodeEnum getByCode(Integer code) {
        for (ToolRespCodeEnum value : values()) {
            if (value.same(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据 code 获取对应枚举值
     *
     * @param code        code
     * @param defaultEnum 默认值
     * @return 对应枚举，匹配不到返回 defaultEnum
     */
    public static ToolRespCodeEnum getByCode(Integer code, ToolRespCodeEnum defaultEnum) {
        ToolRespCodeEnum result = getByCode(code);
        return result != null ? result : defaultEnum;
    }
}
