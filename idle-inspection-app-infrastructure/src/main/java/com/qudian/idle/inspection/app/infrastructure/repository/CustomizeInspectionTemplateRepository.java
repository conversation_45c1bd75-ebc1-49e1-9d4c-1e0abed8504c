package com.qudian.idle.inspection.app.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Repository;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qudian.idle.inspection.app.api.enums.DeleteFlagEnum;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.CustomizeInspectionTemplatePO;
import com.qudian.idle.inspection.app.infrastructure.repository.database.mapper.CustomizeInspectionTemplateMapper;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-14 14:58:54
 */
@Repository
@Slf4j
public class CustomizeInspectionTemplateRepository extends ServiceImpl<CustomizeInspectionTemplateMapper, CustomizeInspectionTemplatePO>{


    @Resource
    private CustomizeInspectionTemplateMapper customizeInspectionTemplateMapper;

    private LambdaQueryWrapper<CustomizeInspectionTemplatePO> getQueryWrapper() {
        LambdaQueryWrapper<CustomizeInspectionTemplatePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomizeInspectionTemplatePO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return queryWrapper;
    }


    private LambdaUpdateWrapper<CustomizeInspectionTemplatePO> getUpdateWrapper() {
        LambdaUpdateWrapper<CustomizeInspectionTemplatePO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CustomizeInspectionTemplatePO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return updateWrapper;
    }

}