package com.qudian.idle.inspection.app.infrastructure.repository.database.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;


/**
 * 任务分类表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-13 16:45:45
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("task_categories")
@NoArgsConstructor
@AllArgsConstructor
public class TaskCategoriesPO extends BasePO {
	private static final long serialVersionUID = 1L;

	/**
	 * 分类名称
	 */
	private String name;
	/**
	 * 父分类ID，顶级分类为NULL
	 */
	private Long parentId;
	/**
	 * 分类级别，从1开始
	 */
	private Integer level;
	/**
	 * 状态：0-禁用，1-启用
	 */
	private Integer status;
	/**
	 * 分类图片路径
	 */
	private String image;

}
