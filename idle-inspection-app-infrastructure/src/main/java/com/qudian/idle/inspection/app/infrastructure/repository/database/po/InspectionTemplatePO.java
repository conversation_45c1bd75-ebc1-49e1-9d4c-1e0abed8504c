package com.qudian.idle.inspection.app.infrastructure.repository.database.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.*;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;


/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-14 14:58:54
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("inspection_template")
@NoArgsConstructor
@AllArgsConstructor
public class InspectionTemplatePO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;
	/**
	 * 模板名称
	 */
	private String name;
	/**
	 * 模板描述
	 */
	private String desc;
	/**
	 * 模板类型
	 */
	private Integer type;
	/**
	 * 状态 1启用 0禁用
	 */
	private Integer status;
	/**
	 * 包含的流程 1售前 2售后 3疑难售前 4 疑难售后 12售前售后 1234售前售后+疑难售前售后
	 */
	private Integer processes;
	/**
	 * 任务分配方式 0手动 1自动分配
	 */
	private Integer taskAllocationMethod;
	/**
	 * 任务自动分配策略ID
	 */
	private Integer taskAllocationStrategyId;
	/**
	 * 任务限制开关 1开 0关
	 */
	private Integer taskConstraintsSwitch;
	/**
	 * sku关联商品确认开关 1开 0关
	 */
	private Integer skuRelationSwitch;
	/**
	 * 创建人ID
	 */
	private Long creatorId;
	/**
	 * 创建人名称
	 */
	private String creatorName;
	/**
	 * 创建时间
	 */
	private LocalDateTime createdTime;
	/**
	 * 更新时间
	 */
	private LocalDateTime updatedTime;
	/**
	 * 软删除标识，0表示未删除，1表示已删除
	 */
	private Integer deleteFlag;

}
