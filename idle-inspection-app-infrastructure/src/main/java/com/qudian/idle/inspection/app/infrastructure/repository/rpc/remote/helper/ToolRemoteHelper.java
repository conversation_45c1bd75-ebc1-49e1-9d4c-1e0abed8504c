package com.qudian.idle.inspection.app.infrastructure.repository.rpc.remote.helper;


import com.qudian.idle.inspection.app.common.utils.json.JsonUtil;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.enums.ToolRespCodeEnum;
import com.qudian.lme.base.vo.BaseResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ToolRemoteHelper {


    public boolean invokeSuccess(BaseResponseVo resp) {
        if (resp == null || resp.getCode() == null) {
            log.info("resp:{}为空!", JsonUtil.toJsonString(resp));
            return false;
        }
        return ToolRespCodeEnum.SUCCESS.same(resp.getCode());
    }
}
