package com.qudian.idle.inspection.app.infrastructure.repository.database.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;


/**
 * 岗位表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-13 16:45:44
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("job")
@NoArgsConstructor
@AllArgsConstructor
public class JobPO extends BasePO {
	private static final long serialVersionUID = 1L;

	/**
	 * 岗位id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;
	/**
	 * 岗位名称
	 */
	private String name;
	/**
	 * 品牌分类规则标识，0-不启用，1-启用
	 */
	private Boolean brandCategoryRuleFlag;

	/**
	 * 所有品牌标识
	 */
	private Boolean allBrandFlag;

	/**
	 * 所有分类标识
	 */
	private Boolean allCategoryFlag;


	/**
	 * 特殊SKU规则标识，0-不启用，1-启用
	 */
	private Boolean specialSkuRuleFlag;

}
