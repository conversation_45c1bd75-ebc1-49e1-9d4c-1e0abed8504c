package com.qudian.idle.inspection.app.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Repository;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qudian.idle.inspection.app.api.enums.DeleteFlagEnum;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.RelationJobLevelPO;
import com.qudian.idle.inspection.app.infrastructure.repository.database.mapper.RelationJobLevelMapper;

/**
 * 岗位-任务等级关联表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-13 17:02:14
 */
@Repository
@Slf4j
public class RelationJobLevelRepository extends ServiceImpl<RelationJobLevelMapper, RelationJobLevelPO>{


    @Resource
    private RelationJobLevelMapper relationJobLevelMapper;

    private LambdaQueryWrapper<RelationJobLevelPO> getQueryWrapper() {
        LambdaQueryWrapper<RelationJobLevelPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RelationJobLevelPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return queryWrapper;
    }


    private LambdaUpdateWrapper<RelationJobLevelPO> getUpdateWrapper() {
        LambdaUpdateWrapper<RelationJobLevelPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RelationJobLevelPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return updateWrapper;
    }

}