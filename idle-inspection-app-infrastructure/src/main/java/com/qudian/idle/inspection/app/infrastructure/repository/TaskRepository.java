package com.qudian.idle.inspection.app.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Repository;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qudian.idle.inspection.app.api.enums.DeleteFlagEnum;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.TaskPO;
import com.qudian.idle.inspection.app.infrastructure.repository.database.mapper.TaskMapper;

/**
 * 任务主表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-13 17:02:13
 */
@Repository
@Slf4j
public class TaskRepository extends ServiceImpl<TaskMapper, TaskPO>{


    @Resource
    private TaskMapper taskMapper;

    private LambdaQueryWrapper<TaskPO> getQueryWrapper() {
        LambdaQueryWrapper<TaskPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return queryWrapper;
    }


    private LambdaUpdateWrapper<TaskPO> getUpdateWrapper() {
        LambdaUpdateWrapper<TaskPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TaskPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return updateWrapper;
    }

}