package com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.external.tool;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class PhoneCheckResp implements Serializable {

    private String origin;
    private String result;
    private boolean success;

    @JSONField(name = "is_mobile")
    private Boolean rightMobile;

}
