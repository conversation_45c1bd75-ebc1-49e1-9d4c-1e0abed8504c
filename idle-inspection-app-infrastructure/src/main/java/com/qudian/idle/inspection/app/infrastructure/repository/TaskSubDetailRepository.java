package com.qudian.idle.inspection.app.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Repository;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qudian.idle.inspection.app.api.enums.DeleteFlagEnum;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.TaskSubDetailPO;
import com.qudian.idle.inspection.app.infrastructure.repository.database.mapper.TaskSubDetailMapper;

/**
 * 子任务详情表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-13 17:02:13
 */
@Repository
@Slf4j
public class TaskSubDetailRepository extends ServiceImpl<TaskSubDetailMapper, TaskSubDetailPO>{


    @Resource
    private TaskSubDetailMapper taskSubDetailMapper;

    private LambdaQueryWrapper<TaskSubDetailPO> getQueryWrapper() {
        LambdaQueryWrapper<TaskSubDetailPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskSubDetailPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return queryWrapper;
    }


    private LambdaUpdateWrapper<TaskSubDetailPO> getUpdateWrapper() {
        LambdaUpdateWrapper<TaskSubDetailPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TaskSubDetailPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return updateWrapper;
    }

}