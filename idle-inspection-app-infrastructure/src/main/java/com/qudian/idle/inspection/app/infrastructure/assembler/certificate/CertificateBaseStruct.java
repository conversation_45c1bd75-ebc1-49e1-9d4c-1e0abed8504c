package com.qudian.idle.inspection.app.infrastructure.assembler.certificate;

import com.qudian.idle.inspection.app.api.vo.response.certificate.CertificateBaseShowRespVO;
import com.qudian.idle.inspection.app.common.utils.json.JsonUtil;
import com.qudian.idle.inspection.app.infrastructure.gatewayImpl.dto.certificate.CertificateGoodsSnapshotRO;
import com.qudian.idle.inspection.app.infrastructure.gatewayImpl.dto.certificate.CertificateOrderSnapshotRO;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.certificate.CertificatePO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <p>文件名称:com.qudian.idle.inspection.app.infrastructure.assembler.certificate.CertificateBaseStruct</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/21
 */
@Mapper
public interface CertificateBaseStruct {

    @Mappings({
            @Mapping(source = "id", target = "certificateInfo.certificateId"),
            @Mapping(source = "certificateNo", target = "certificateInfo.certificateNo"),
            @Mapping(source = "antiCounterfeitNo", target = "certificateInfo.antiCounterfeitNo"),
            @Mapping(source = "verdict", target = "certificateInfo.verdict"),
            @Mapping(source = "queryCnt", target = "certificateInfo.queryCnt"),
            @Mapping(source = "lastQueryTime", target = "certificateInfo.lastQueryTime"),
            @Mapping(target = "certificateInfo.currentQueryTime", expression = "java(getCurrentTime())"),
            @Mapping(source = "goodsSnapshot", target = "goodsInfo", qualifiedByName = "toGoods"),
            @Mapping(source = "orderSnapshot", target = "orderInfo", qualifiedByName = "toOrder"),
    })
    CertificateBaseShowRespVO Po2ShowVo(CertificatePO po);

    default String getCurrentTime() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    @Named("toGoods")
    default CertificateBaseShowRespVO.CertificateInnerGoodsSnapshotRespVO toGoods(String json) {
        CertificateGoodsSnapshotRO dto = JsonUtil.objToTargetClass(json, CertificateGoodsSnapshotRO.class);
        return new CertificateBaseShowRespVO.CertificateInnerGoodsSnapshotRespVO()
                .setGoodsName(dto.goodsName())
                .setBrand(dto.brand())
                .setMainImages(dto.mainImages());
    }

    @Named("toOrder")
    default CertificateBaseShowRespVO.CertificateInnerOrderSnapshotRespVO toOrder(String json) {
        CertificateOrderSnapshotRO dto = JsonUtil.objToTargetClass(json, CertificateOrderSnapshotRO.class);
        return new CertificateBaseShowRespVO.CertificateInnerOrderSnapshotRespVO()
                .setAmount(dto.amount())
                .setOrderTime(dto.orderTime())
                .setOrderSource(dto.orderSource())
                .setPurchaserName(dto.purchaserName());
    }
}
