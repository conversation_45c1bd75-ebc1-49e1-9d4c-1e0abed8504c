package com.qudian.idle.inspection.app.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Repository;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qudian.idle.inspection.app.api.enums.DeleteFlagEnum;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.InspectionTemplateSkuRelationConfirmPO;
import com.qudian.idle.inspection.app.infrastructure.repository.database.mapper.InspectionTemplateSkuRelationConfirmMapper;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-14 14:58:54
 */
@Repository
@Slf4j
public class InspectionTemplateSkuRelationConfirmRepository extends ServiceImpl<InspectionTemplateSkuRelationConfirmMapper, InspectionTemplateSkuRelationConfirmPO>{


    @Resource
    private InspectionTemplateSkuRelationConfirmMapper inspectionTemplateSkuRelationConfirmMapper;

    private LambdaQueryWrapper<InspectionTemplateSkuRelationConfirmPO> getQueryWrapper() {
        LambdaQueryWrapper<InspectionTemplateSkuRelationConfirmPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InspectionTemplateSkuRelationConfirmPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return queryWrapper;
    }


    private LambdaUpdateWrapper<InspectionTemplateSkuRelationConfirmPO> getUpdateWrapper() {
        LambdaUpdateWrapper<InspectionTemplateSkuRelationConfirmPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(InspectionTemplateSkuRelationConfirmPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return updateWrapper;
    }

}