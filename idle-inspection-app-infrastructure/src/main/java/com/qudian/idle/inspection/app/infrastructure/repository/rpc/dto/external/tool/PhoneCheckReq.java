package com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.external.tool;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PhoneCheckReq implements Serializable {

    /**
     * 手机号列表
     */
    private List<String> mobiles;

    /**
     * 国家码
     */
    @JsonProperty("country_code")
    @JSONField(name = "country_code")
    private String countryCode;

}
