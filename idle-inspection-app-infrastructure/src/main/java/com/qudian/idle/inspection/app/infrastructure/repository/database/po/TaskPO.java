package com.qudian.idle.inspection.app.infrastructure.repository.database.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 任务主表
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("task")
@NoArgsConstructor
@AllArgsConstructor
public class TaskPO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 任务号
	 */
	private String taskNo;

	/**
	 * 订单号
	 */
	private String orderNo;

	/**
	 * 品牌id
	 */
	private String productBrandId;

	/**
	 * 商品分类id
	 */
	private String productCategoryId;

	/**
	 * SKU ID
	 */
	private String skuId;

	/**
	 * sn
	 */
	private String sn;

	/**
	 * 任务分类id，如1真假鉴定、2瑕疵鉴定、3成色鉴定、4拍照等
	 */
	private Long taskCategoryId;

	/**
	 * 来源，可选值如1个人、2商家、3品牌
	 */
	private Integer source;

	/**
	 * 批次号
	 */
	private String batchNo;

	/**
	 * 模板ID
	 */
	private String templateId;

	/**
	 * 销售阶段 0 售前  1售后
	 */
	private Integer salesStage;

	/**
	 * 是否疑难件 0 不是 1 是
	 */
	private Integer difficultCase;

	/**
	 * 任务截止时间
	 */
	private LocalDateTime deadlineTime;

	/**
	 * 任务状态，如 0未开始、1进行中、2已完成、3挂起、4待定、5自动完成
	 */
	private Integer status;

	/**
	 * 任务所需人数
	 */
	private Integer requiredPeople;

	/**
	 * 已认领人数
	 */
	private Integer claimedPeople;

	/**
	 * 任务限制开关 1开 0关
	 */
	private Integer taskConstraintsSwitch;

	/**
	 * sku关联商品确认开关 1开 0关
	 */
	private Integer skuRelationSwitch;

	/**
	 * 综合结果（1-未通过、2-通过、3-99新 4-疑难件）
	 */
	private Integer overallResult;

	/**
	 * 创建时间
	 */
	private LocalDateTime createdTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updatedTime;

	/**
	 * 软删除标识，0表示未删除，非0表示已删除
	 */
	private Integer deleteFlag;
}