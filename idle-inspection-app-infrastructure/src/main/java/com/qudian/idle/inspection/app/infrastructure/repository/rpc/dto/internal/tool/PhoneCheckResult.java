package com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.internal.tool;

import lombok.Data;

import java.io.Serializable;

@Data
public class PhoneCheckResult implements Serializable {

    /**
     * 原始手机号
     */
    private String originPhone;

    /**
     * 格式化后的手机号
     */
    private String rightPhone;

    /**
     * 校验结果
     */
    private Boolean checkResult;

    /**
     * 错误信息
     */
    private String failReason;



}
