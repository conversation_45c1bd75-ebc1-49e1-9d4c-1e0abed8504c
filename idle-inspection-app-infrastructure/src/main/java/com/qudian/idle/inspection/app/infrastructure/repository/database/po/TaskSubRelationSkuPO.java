package com.qudian.idle.inspection.app.infrastructure.repository.database.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 子任务关联的sku校对
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "task_sub_relation_sku")
@NoArgsConstructor
@AllArgsConstructor
public class TaskSubRelationSkuPO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 子任务号
     */
    private String taskSubNo;

    /**
     * 本步具体内容包含模版快照（JSON）
     */
    private String detail;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 用户输入的位置值
     */
    private String positionInputValue;

    /**
     * 用户输入的描述内容
     */
    private String descInputContent;

    /**
     * 用户输入的照片路径（JSON 数组）
     */
    private String descInputPhotos;

    /**
     * 用户输入的比对图照片路径（JSON 数组）
     */
    private String descInputCompPhotos;

    /**
     * 用户输入的评级结果（评分或单选值）
     */
    private Integer gradingInputResult;

    /**
     * 用户输入的评级结果描述
     */
    private String gradingInputResultDesc;

    /**
     * 执行人名称
     */
    private String userName;

    /**
     * 执行人ID
     */
    private Long userId;

    /**
     * 软删除标识，0表示未删除，1表示已删除
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}