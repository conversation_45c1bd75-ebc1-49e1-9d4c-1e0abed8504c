package com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.internal.tool;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SendSmsCommand implements Serializable {


    /**
     * 手机号
     */
    private List<String> mobiles;

    /**
     * 业务
     */
    private String business;
    /**
     * 国家
     */
    private String countryCode;

    /**
     * 短信业务类型
     */
    private Integer type;

    /**
     * 短信内容
     */
    private String content;

    /**
     * 短信业务类型:1 -验证码 2 - 通知
     * @see
     */
    private String code;

    /**
     * uuid
     */
    private String uuid;

    /**
     * 是否异步发送
     */
    private boolean async;

    /**
     * 发送者名称。USER:用户 ORDER:订单
     * @see
     */
    private String operatorName;



}
