package com.qudian.idle.inspection.app.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Repository;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qudian.idle.inspection.app.api.enums.DeleteFlagEnum;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.InspectionTemplateStepPO;
import com.qudian.idle.inspection.app.infrastructure.repository.database.mapper.InspectionTemplateStepMapper;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-14 14:58:54
 */
@Repository
@Slf4j
public class InspectionTemplateStepRepository extends ServiceImpl<InspectionTemplateStepMapper, InspectionTemplateStepPO>{


    @Resource
    private InspectionTemplateStepMapper inspectionTemplateStepMapper;

    private LambdaQueryWrapper<InspectionTemplateStepPO> getQueryWrapper() {
        LambdaQueryWrapper<InspectionTemplateStepPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InspectionTemplateStepPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return queryWrapper;
    }


    private LambdaUpdateWrapper<InspectionTemplateStepPO> getUpdateWrapper() {
        LambdaUpdateWrapper<InspectionTemplateStepPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(InspectionTemplateStepPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return updateWrapper;
    }

}