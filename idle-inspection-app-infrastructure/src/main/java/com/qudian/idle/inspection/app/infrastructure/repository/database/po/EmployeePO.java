package com.qudian.idle.inspection.app.infrastructure.repository.database.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;


/**
 * 员工表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-13 16:45:45
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("employee")
@NoArgsConstructor
@AllArgsConstructor
public class EmployeePO extends BasePO {
    private static final long serialVersionUID = 1L;

    /**
     * 员工id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 姓名
     */
    private String name;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 来源系统 1:检测端，2:仓库端
     *
     * @see com.qudian.idle.inspection.app.api.enums.employee.EmployeeSourceSystemEnum
     */
    private Integer sourceSystem;
    /**
     * 状态 0-初始化 1-在职，2-离职
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;

}
