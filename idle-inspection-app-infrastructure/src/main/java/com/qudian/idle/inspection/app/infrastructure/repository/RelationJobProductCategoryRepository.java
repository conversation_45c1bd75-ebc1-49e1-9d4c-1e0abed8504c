package com.qudian.idle.inspection.app.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Repository;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qudian.idle.inspection.app.api.enums.DeleteFlagEnum;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.RelationJobProductCategoryPO;
import com.qudian.idle.inspection.app.infrastructure.repository.database.mapper.RelationJobProductCategoryMapper;

/**
 * 岗位-商品分类关联表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-13 17:02:13
 */
@Repository
@Slf4j
public class RelationJobProductCategoryRepository extends ServiceImpl<RelationJobProductCategoryMapper, RelationJobProductCategoryPO>{


    @Resource
    private RelationJobProductCategoryMapper relationJobProductCategoryMapper;

    private LambdaQueryWrapper<RelationJobProductCategoryPO> getQueryWrapper() {
        LambdaQueryWrapper<RelationJobProductCategoryPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RelationJobProductCategoryPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return queryWrapper;
    }


    private LambdaUpdateWrapper<RelationJobProductCategoryPO> getUpdateWrapper() {
        LambdaUpdateWrapper<RelationJobProductCategoryPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RelationJobProductCategoryPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return updateWrapper;
    }

}