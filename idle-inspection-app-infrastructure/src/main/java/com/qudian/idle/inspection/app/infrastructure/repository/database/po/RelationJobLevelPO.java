package com.qudian.idle.inspection.app.infrastructure.repository.database.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;


/**
 * 岗位-任务等级关联表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-13 16:45:45
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("relation_job_level")
@NoArgsConstructor
@AllArgsConstructor
public class RelationJobLevelPO extends BasePO {

	/**
	 * 岗位id
	 */
	private Long jobId;
	/**
	 * 岗位级别 1:初级,2:中级,3:高级
	 */
	private Integer level;

}
