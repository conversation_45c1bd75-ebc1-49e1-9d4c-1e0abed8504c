package com.qudian.idle.inspection.app.infrastructure.repository.rpc.remote;


import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.internal.tool.LanguageDetailQuery;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.internal.tool.LanguageDetailResult;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.internal.tool.PhoneCheckQuery;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.internal.tool.PhoneCheckResult;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.internal.tool.SendSmsCommand;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.internal.tool.SendSmsResult;

import java.util.List;

public interface ToolRemote {

    /**
     * 校验手机号
     * @param query
     * @return
     */
    PhoneCheckResult checkPhone(PhoneCheckQuery query);

    List<LanguageDetailResult> getLanguageList(LanguageDetailQuery query);

    /**
     * 发生验证码
     * @param command
     * @return
     */
    void sendSmsMessage(SendSmsCommand command);

}
