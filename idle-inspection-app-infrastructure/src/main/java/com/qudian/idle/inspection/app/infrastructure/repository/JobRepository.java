package com.qudian.idle.inspection.app.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Repository;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qudian.idle.inspection.app.api.enums.DeleteFlagEnum;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.JobPO;
import com.qudian.idle.inspection.app.infrastructure.repository.database.mapper.JobMapper;

/**
 * 岗位表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-13 17:02:13
 */
@Repository
@Slf4j
public class JobRepository extends ServiceImpl<JobMapper, JobPO>{


    @Resource
    private JobMapper jobMapper;

    private LambdaQueryWrapper<JobPO> getQueryWrapper() {
        LambdaQueryWrapper<JobPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(JobPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return queryWrapper;
    }


    private LambdaUpdateWrapper<JobPO> getUpdateWrapper() {
        LambdaUpdateWrapper<JobPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(JobPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return updateWrapper;
    }

}