package com.qudian.idle.inspection.app.infrastructure.helper;


import com.qudian.idle.inspection.app.api.vo.response.common.CommandResultRespVO;
import com.qudian.idle.inspection.app.common.enums.BizErrorEnum;
import com.qudian.idle.inspection.app.infrastructure.handler.LanguageHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class CommonHelper {

    @Resource
    private LanguageHandler languageHandler;

    public CommandResultRespVO buildCommandErrorResult(BizErrorEnum bizErrorEnum) {
        if (bizErrorEnum == null) {
            bizErrorEnum = BizErrorEnum.SYSTEM_ERR;
        }
        String failMessage = languageHandler.fetchFromAppLanguage(bizErrorEnum.getI18nKey(), bizErrorEnum.getMsg());
        return CommandResultRespVO.buildError(failMessage);
    }

    public CommandResultRespVO buildCommandSuccessResult(Long operateId) {
        return CommandResultRespVO.buildSuccess(operateId);
    }

}
