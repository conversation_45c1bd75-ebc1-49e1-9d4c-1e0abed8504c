// idle-inspection-app-infrastructure/src/main/java/com/qudian/idle/inspection/app/infrastructure/repository/database/po/TaskPoolPO.java
package com.qudian.idle.inspection.app.infrastructure.repository.database.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 任务池 岗位-任务可接关系
 */
@Data
@TableName("task_pool")
public class TaskPoolPO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("task_no")
    private String taskNo;

    @TableField("task_sub_no")
    private Long taskSubNo;

    @TableField("job_id")
    private Long jobId;

    @TableField("job_level")
    private Integer jobLevel;

    @TableField("created_time")
    private LocalDateTime createdTime;

    @TableField("updated_time")
    private LocalDateTime updatedTime;

    @TableField("delete_flag")
    private Integer deleteFlag;
}