package com.qudian.idle.inspection.app.infrastructure.repository.database.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.*;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;


/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-14 14:58:54
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("inspection_template_sku_relation_confirm")
@NoArgsConstructor
@AllArgsConstructor
public class InspectionTemplateSkuRelationConfirmPO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;
	/**
	 * 模板id
	 */
	private Long templateId;
	/**
	 * 描述字段开关
	 */
	private Integer descSwitch;
	/**
	 * 描述名称
	 */
	private String descName;
	/**
	 * 描述提示
	 */
	private String descTips;
	/**
	 * 描述的描述
	 */
	private String descDesc;
	/**
	 * 描述值类型 1图文 2文
	 */
	private Integer descValueType;
	/**
	 * 图文设置 - 拍摄照片 1是 0否
	 */
	private Integer descGtsPhoto;
	/**
	 * 图文设置 - 必填 1是 0否
	 */
	private Integer descGtsRequired;
	/**
	 * 图文设置 - 对比图 1是0否
	 */
	private Integer descGtsCompChart;
	/**
	 * 图文设置 - 照片数量>多少张
	 */
	private Integer descGtsPhotoNum;
	/**
	 * 图文设置 - 文字描述 < 多少字
	 */
	private Integer descGtsTextNum;
	/**
	 * 创建时间
	 */
	private LocalDateTime createdTime;
	/**
	 * 更新时间
	 */
	private LocalDateTime updatedTime;
	/**
	 * 软删除标识，0表示未删除，1表示已删除
	 */
	private Integer deleteFlag;

}
