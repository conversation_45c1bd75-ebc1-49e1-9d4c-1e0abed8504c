package com.qudian.idle.inspection.app.infrastructure.repository.database.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.*;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;


/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-14 14:58:53
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("customize_inspection_template_step")
@NoArgsConstructor
@AllArgsConstructor
public class CustomizeInspectionTemplateStepPO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;
	/**
	 * 自定义模板id
	 */
	private Long customizeTemplateId;
	/**
	 * 子任务类型 1瑕疵检测 2真假检测 3成色检测 4拍照留档
	 */
	private Integer type;
	/**
	 * 步骤 从1开始,最大999,-1为其他瑕疵（在质检端可重复添加）
	 */
	private Integer step;
	/**
	 * 包含的流程 1售前 2售后 3疑难售前 4 疑难售后 12售前售后 1234售前售后+疑难售前售后
	 */
	private Integer processes;
	/**
	 * 任务分配方式 0手动 1自动分配
	 */
	private Integer taskAllocationMethod;
	/**
	 * 任务自动分配策略ID
	 */
	private Integer taskAllocationStrategyId;
	/**
	 * 位置字段开关
	 */
	private Integer positionSwitch;
	/**
	 * 位置名称
	 */
	private String positionName;
	/**
	 * 位置值的类型 1固定值 2预设值
	 */
	private Integer positionValueType;
	/**
	 * 固定值字符串 或者 预设值json
	 */
	private String positionValue;
	/**
	 * 字段提示
	 */
	private String positionTips;
	/**
	 * 字段说明
	 */
	private String positionDesc;
	/**
	 * 描述字段开关
	 */
	private Integer descSwitch;
	/**
	 * 描述名称
	 */
	private String descName;
	/**
	 * 描述提示
	 */
	private String descTips;
	/**
	 * 描述的描述
	 */
	private String descDesc;
	/**
	 * 描述值类型 1图文 2文
	 */
	private Integer descValueType;
	/**
	 * 图文设置 - 拍摄照片 1是 0否
	 */
	private Integer descGtsPhoto;
	/**
	 * 图文设置 - 必填 1是 0否
	 */
	private Integer descGtsRequired;
	/**
	 * 图文设置 - 对比图 1是0否
	 */
	private Integer descGtsCompChart;
	/**
	 * 图文设置 - 照片数量>多少张
	 */
	private Integer descGtsPhotoNum;
	/**
	 * 图文设置 - 文字描述 < 多少字
	 */
	private Integer descGtsTextNum;
	/**
	 * 评级名称
	 */
	private String gradingName;
	/**
	 * 评级值类型 1评分 2单选
	 */
	private Integer gradingValueType;
	/**
	 * 评级提示
	 */
	private String gradingTips;
	/**
	 * 评级描述
	 */
	private String gradingDesc;
	/**
	 * 评级分数
	 */
	private Integer gradingScore;
	/**
	 * 评级分数描述
	 */
	private String gradingScoreDesc;
	/**
	 * 评级单选
	 */
	private String gradingRadio;
	/**
	 * 创建时间
	 */
	private LocalDateTime createdTime;
	/**
	 * 更新时间
	 */
	private LocalDateTime updatedTime;
	/**
	 * 软删除标识，0表示未删除，1表示已删除
	 */
	private Integer deleteFlag;

}
