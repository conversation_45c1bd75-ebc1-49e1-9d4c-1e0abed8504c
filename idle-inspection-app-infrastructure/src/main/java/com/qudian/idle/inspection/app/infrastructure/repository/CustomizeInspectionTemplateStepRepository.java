package com.qudian.idle.inspection.app.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Repository;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qudian.idle.inspection.app.api.enums.DeleteFlagEnum;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.CustomizeInspectionTemplateStepPO;
import com.qudian.idle.inspection.app.infrastructure.repository.database.mapper.CustomizeInspectionTemplateStepMapper;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-14 14:58:53
 */
@Repository
@Slf4j
public class CustomizeInspectionTemplateStepRepository extends ServiceImpl<CustomizeInspectionTemplateStepMapper, CustomizeInspectionTemplateStepPO>{


    @Resource
    private CustomizeInspectionTemplateStepMapper customizeInspectionTemplateStepMapper;

    private LambdaQueryWrapper<CustomizeInspectionTemplateStepPO> getQueryWrapper() {
        LambdaQueryWrapper<CustomizeInspectionTemplateStepPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomizeInspectionTemplateStepPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return queryWrapper;
    }


    private LambdaUpdateWrapper<CustomizeInspectionTemplateStepPO> getUpdateWrapper() {
        LambdaUpdateWrapper<CustomizeInspectionTemplateStepPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CustomizeInspectionTemplateStepPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return updateWrapper;
    }

}