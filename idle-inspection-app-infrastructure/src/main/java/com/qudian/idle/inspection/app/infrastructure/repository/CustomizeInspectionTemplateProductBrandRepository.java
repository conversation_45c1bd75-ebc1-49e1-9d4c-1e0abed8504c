package com.qudian.idle.inspection.app.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Repository;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qudian.idle.inspection.app.api.enums.DeleteFlagEnum;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.CustomizeInspectionTemplateProductBrandPO;
import com.qudian.idle.inspection.app.infrastructure.repository.database.mapper.CustomizeInspectionTemplateProductBrandMapper;

/**
 * 岗位-商品品牌关联表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-14 14:58:54
 */
@Repository
@Slf4j
public class CustomizeInspectionTemplateProductBrandRepository extends ServiceImpl<CustomizeInspectionTemplateProductBrandMapper, CustomizeInspectionTemplateProductBrandPO>{


    @Resource
    private CustomizeInspectionTemplateProductBrandMapper customizeInspectionTemplateProductBrandMapper;

    private LambdaQueryWrapper<CustomizeInspectionTemplateProductBrandPO> getQueryWrapper() {
        LambdaQueryWrapper<CustomizeInspectionTemplateProductBrandPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomizeInspectionTemplateProductBrandPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return queryWrapper;
    }


    private LambdaUpdateWrapper<CustomizeInspectionTemplateProductBrandPO> getUpdateWrapper() {
        LambdaUpdateWrapper<CustomizeInspectionTemplateProductBrandPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CustomizeInspectionTemplateProductBrandPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return updateWrapper;
    }

}