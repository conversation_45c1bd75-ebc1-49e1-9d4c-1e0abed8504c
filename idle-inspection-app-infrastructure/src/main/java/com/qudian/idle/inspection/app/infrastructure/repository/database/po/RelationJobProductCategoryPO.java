package com.qudian.idle.inspection.app.infrastructure.repository.database.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;


/**
 * 岗位-商品分类关联表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-13 16:45:44
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("relation_job_product_category")
@NoArgsConstructor
@AllArgsConstructor
public class RelationJobProductCategoryPO extends BasePO {
	private static final long serialVersionUID = 1L;


	/**
	 * 岗位id
	 */
	private Long jobId;
	/**
	 * 商品分类id
	 */
	private String productCategoryId;


}
