package com.qudian.idle.inspection.app.infrastructure.assembler;

import com.qudian.idle.inspection.app.common.helper.MapStructHelper;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.external.tool.LanguageDetailReq;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.external.tool.PhoneCheckReq;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.external.tool.SendSmsReqDTO;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.internal.tool.LanguageDetailQuery;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.internal.tool.PhoneCheckQuery;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.internal.tool.SendSmsCommand;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(imports = {StringUtils.class},componentModel = "spring",uses = {MapStructHelper.class})
public interface ToolRemoteAssembler {

    ToolRemoteAssembler INSTANCE = Mappers.getMapper(ToolRemoteAssembler.class);

    LanguageDetailReq toLanguageDetailReq(LanguageDetailQuery query);

    @Mapping(target = "mobiles",source = "phone",qualifiedByName = "strToSingleList")
    PhoneCheckReq toCheckPhoneReq(PhoneCheckQuery query);

    @Mapping(target = "mobiles",source = "command.mobiles",qualifiedByName = "strListToArray")
    SendSmsReqDTO toSendSmsReq(SendSmsCommand command);
}
