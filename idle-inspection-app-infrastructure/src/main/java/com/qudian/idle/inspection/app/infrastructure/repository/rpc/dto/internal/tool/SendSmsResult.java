package com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.internal.tool;

import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.external.tool.SendSmsResultDTO;
import lombok.Data;

import java.io.Serializable;

@Data
public class SendSmsResult implements Serializable {


    /**
     * 手机号
     */
    private Boolean success;

    /**
     * 错误信息
     */
    private String failedMessage;

    public static SendSmsResult buildFailed(String failedMessage) {
        SendSmsResult result = new SendSmsResult();
        result.setSuccess(false);
        result.setFailedMessage(failedMessage);
        return result;
    }

    public static SendSmsResult buildSuccess() {
        SendSmsResult result = new SendSmsResult();
        result.setSuccess(true);
        return result;
    }


}
