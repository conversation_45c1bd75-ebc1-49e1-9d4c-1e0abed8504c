package com.qudian.idle.inspection.app.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Repository;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qudian.idle.inspection.app.api.enums.DeleteFlagEnum;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.TaskCategoriesPO;
import com.qudian.idle.inspection.app.infrastructure.repository.database.mapper.TaskCategoriesMapper;

/**
 * 任务分类表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-13 17:02:14
 */
@Repository
@Slf4j
public class TaskCategoriesRepository extends ServiceImpl<TaskCategoriesMapper, TaskCategoriesPO>{


    @Resource
    private TaskCategoriesMapper taskCategoriesMapper;

    private LambdaQueryWrapper<TaskCategoriesPO> getQueryWrapper() {
        LambdaQueryWrapper<TaskCategoriesPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskCategoriesPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return queryWrapper;
    }


    private LambdaUpdateWrapper<TaskCategoriesPO> getUpdateWrapper() {
        LambdaUpdateWrapper<TaskCategoriesPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TaskCategoriesPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return updateWrapper;
    }

}