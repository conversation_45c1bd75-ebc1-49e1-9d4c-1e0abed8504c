package com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.external.tool;

import lombok.Data;

@Data
public class SendSmsResultDTO {

    private Boolean success;

    private String failedMessage;


    public static SendSmsResultDTO buildFailed(String failedMessage) {
        SendSmsResultDTO result = new SendSmsResultDTO();
        result.setSuccess(false);
        result.setFailedMessage(failedMessage);
        return result;
    }

    public static SendSmsResultDTO buildSuccess() {
        SendSmsResultDTO result = new SendSmsResultDTO();
        result.setSuccess(true);
        return result;
    }
}
