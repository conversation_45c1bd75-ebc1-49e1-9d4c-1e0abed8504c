package com.qudian.idle.inspection.app.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Repository;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qudian.idle.inspection.app.api.enums.DeleteFlagEnum;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.EmployeePO;
import com.qudian.idle.inspection.app.infrastructure.repository.database.mapper.EmployeeMapper;

/**
 * 员工表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-13 17:02:14
 */
@Repository
@Slf4j
public class EmployeeRepository extends ServiceImpl<EmployeeMapper, EmployeePO>{


    @Resource
    private EmployeeMapper employeeMapper;

    private LambdaQueryWrapper<EmployeePO> getQueryWrapper() {
        LambdaQueryWrapper<EmployeePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmployeePO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return queryWrapper;
    }


    private LambdaUpdateWrapper<EmployeePO> getUpdateWrapper() {
        LambdaUpdateWrapper<EmployeePO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(EmployeePO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return updateWrapper;
    }

    public EmployeePO selectByMobile(String mobile) {
        LambdaQueryWrapper<EmployeePO> queryWrapper = getQueryWrapper();
        queryWrapper.eq(EmployeePO::getMobile, mobile);
        queryWrapper.last("limit 1");
        return employeeMapper.selectOne(queryWrapper);
    }
}