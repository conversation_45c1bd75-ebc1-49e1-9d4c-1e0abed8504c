package com.qudian.idle.inspection.app.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Repository;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qudian.idle.inspection.app.api.enums.DeleteFlagEnum;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.TaskSubPO;
import com.qudian.idle.inspection.app.infrastructure.repository.database.mapper.TaskSubMapper;

/**
 * 子任务表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-13 17:02:13
 */
@Repository
@Slf4j
public class TaskSubRepository extends ServiceImpl<TaskSubMapper, TaskSubPO>{


    @Resource
    private TaskSubMapper taskSubMapper;

    private LambdaQueryWrapper<TaskSubPO> getQueryWrapper() {
        LambdaQueryWrapper<TaskSubPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskSubPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return queryWrapper;
    }


    private LambdaUpdateWrapper<TaskSubPO> getUpdateWrapper() {
        LambdaUpdateWrapper<TaskSubPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TaskSubPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return updateWrapper;
    }

}