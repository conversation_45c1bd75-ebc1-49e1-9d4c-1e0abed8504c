package com.qudian.idle.inspection.app.infrastructure.repository.database.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 基础PO类，包含公共字段
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class BasePO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 创建人id
     */
    private String creatorId;
    /**
     * 创建人名称
     */
    private String creatorName;
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    /**
     * 最后编辑人id
     */
    private String updaterId;
    /**
     * 最后编辑人名称
     */
    private String updaterName;
    /**
     * 软删除标识，0表示未删除，非0表示已删除
     */
    @TableLogic
    private Long deleteFlag;
}
