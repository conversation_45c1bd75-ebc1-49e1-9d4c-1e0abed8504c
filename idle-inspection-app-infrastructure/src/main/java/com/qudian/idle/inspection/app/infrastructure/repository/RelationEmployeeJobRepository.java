package com.qudian.idle.inspection.app.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Repository;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qudian.idle.inspection.app.api.enums.DeleteFlagEnum;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.RelationEmployeeJobPO;
import com.qudian.idle.inspection.app.infrastructure.repository.database.mapper.RelationEmployeeJobMapper;

/**
 * 员工-岗位关联表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-13 17:02:14
 */
@Repository
@Slf4j
public class RelationEmployeeJobRepository extends ServiceImpl<RelationEmployeeJobMapper, RelationEmployeeJobPO>{


    @Resource
    private RelationEmployeeJobMapper relationEmployeeJobMapper;

    private LambdaQueryWrapper<RelationEmployeeJobPO> getQueryWrapper() {
        LambdaQueryWrapper<RelationEmployeeJobPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RelationEmployeeJobPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return queryWrapper;
    }


    private LambdaUpdateWrapper<RelationEmployeeJobPO> getUpdateWrapper() {
        LambdaUpdateWrapper<RelationEmployeeJobPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RelationEmployeeJobPO::getDeleteFlag, DeleteFlagEnum.NORMAL.getCode());
        return updateWrapper;
    }

}