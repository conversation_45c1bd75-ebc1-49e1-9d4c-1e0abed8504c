package com.qudian.idle.inspection.app.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qudian.idle.inspection.app.infrastructure.repository.database.mapper.certificate.CertificateMapper;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.certificate.CertificatePO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * <p>文件名称:com.qudian.idle.inspection.app.infrastructure.repository.CertificatesRepository</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/21
 */
@Repository
@Slf4j
public class CertificateRepository extends ServiceImpl<CertificateMapper, CertificatePO> {

    public Optional<CertificatePO> selectBySn(String sn) {
        LambdaQueryWrapper<CertificatePO> queryWrapper = new LambdaQueryWrapper<CertificatePO>().eq(CertificatePO::getSn, sn);
        return this.getOneOpt(queryWrapper);
    }
}
