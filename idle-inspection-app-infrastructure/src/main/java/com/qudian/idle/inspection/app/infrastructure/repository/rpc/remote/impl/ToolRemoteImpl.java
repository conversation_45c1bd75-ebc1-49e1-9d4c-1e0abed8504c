package com.qudian.idle.inspection.app.infrastructure.repository.rpc.remote.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.qudian.idle.inspection.app.common.config.ToolRemoteConfig;
import com.qudian.idle.inspection.app.common.utils.http.ToolHttpClient;
import com.qudian.idle.inspection.app.common.utils.json.JsonUtil;
import com.qudian.idle.inspection.app.infrastructure.assembler.ToolRemoteAssembler;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.external.tool.LanguageDetailReq;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.external.tool.PhoneCheckReq;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.external.tool.PhoneCheckResp;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.external.tool.SendSmsReqDTO;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.external.tool.SendSmsResultDTO;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.internal.tool.LanguageDetailQuery;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.internal.tool.LanguageDetailResult;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.internal.tool.PhoneCheckQuery;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.internal.tool.PhoneCheckResult;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.internal.tool.SendSmsCommand;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.internal.tool.SendSmsResult;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.enums.ToolRespCodeEnum;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.remote.ToolRemote;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.remote.helper.ToolRemoteHelper;
import com.qudian.lme.base.vo.BaseResponseVo;
import com.qudian.pdt.toolkit.common.enums.ExceptionEnum;
import com.qudian.pdt.toolkit.common.exception.GlobalI18nException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.List;
import java.util.Optional;


@Service
@Slf4j
public class ToolRemoteImpl implements ToolRemote {

    @Resource
    private ToolRemoteConfig toolRemoteConfig;

    @Resource
    private ToolHttpClient toolHttpClient;

    @Resource
    private ToolRemoteAssembler toolRemoteAssembler;

    @Resource
    private ToolRemoteHelper toolRemoteHelper;


    @Override
    public PhoneCheckResult checkPhone(PhoneCheckQuery query) {
        log.info("请求tool服务,校验手机号,query:{}", JsonUtil.toJsonString(query));
        PhoneCheckReq req = toolRemoteAssembler.toCheckPhoneReq(query);
        log.info("请求tool服务,校验手机号,req:{}", JsonUtil.toJsonString(req));
        BaseResponseVo resp = null;
        try {
            String url = toolRemoteConfig.getAllUrl(toolRemoteConfig.getCheckPhone());
            resp = toolHttpClient.post(url, JsonUtil.toJsonString(req));
            log.info("请求tool服务,校验手机号,resp:{}", JsonUtil.toJsonString(resp));
        } catch (Exception e) {
            log.error("请求tool服务，校验手机号时发生通信异常!error:{}", e.getMessage(), e);
            throw new GlobalI18nException(ExceptionEnum.SECOND_PARTY_ERROR);
        }

        if (!ToolRespCodeEnum.SUCCESS.same(resp.getCode())) {
            log.error("请求tool服务，校验手机号响应返回异常!resp:{}", JsonUtil.toJsonString(resp));
            throw new GlobalI18nException(ExceptionEnum.SECOND_PARTY_ERROR);
        }
        List<PhoneCheckResp> resultData = null;
        if (resp.getData() instanceof JSONArray) {
            JSONArray data = (JSONArray) resp.getData();
            resultData = data.toJavaList(PhoneCheckResp.class);
        } else {
            throw new GlobalI18nException(ExceptionEnum.SECOND_PARTY_ERROR);
        }

        PhoneCheckResult result = new PhoneCheckResult();
        result.setOriginPhone(query.getPhone());
        if (CollectionUtils.isEmpty(resultData)) {
            result.setCheckResult(false);
            return result;
        }

        if (resultData.size() > 1) {
            log.error("请求tool服务，校验手机号时，返回校验结果存在多个!");
        }

        PhoneCheckResp phoneCheckResp = resultData.get(0);
        boolean success = phoneCheckResp.isSuccess() && Boolean.TRUE.equals(phoneCheckResp.getRightMobile());
        result.setCheckResult(success);
        if (success) {
            result.setRightPhone(phoneCheckResp.getResult());
        }
        return result;
    }

    private List<LanguageDetailResult> doGetLanguageList(LanguageDetailQuery query) {
        log.info("调用tool服务查询多语言,query:{}", JsonUtil.toJsonString(query));
        LanguageDetailReq req = toolRemoteAssembler.toLanguageDetailReq(query);
        log.info("调用tool服务查询多语言,req:{}", JsonUtil.toJsonString(req));
        BaseResponseVo resp = null;
        try {
            String url = toolRemoteConfig.getAllUrl(toolRemoteConfig.getLanguageList());
            resp = toolHttpClient.post(url, JsonUtil.toJsonString(req));
            log.info("调用tool服务查询多语言,resp:{}", JsonUtil.toJsonString(resp));
        } catch (Exception e) {
            log.error("调用tool服务查询多语言,通信异常!error:{}", e.getMessage(), e);
            return Lists.newArrayList();
        }

        if (!ToolRespCodeEnum.SUCCESS.same(resp.getCode())) {
            log.error("调用tool服务查询多语言,响应异常!resp:{}", JsonUtil.toJsonString(resp));
            return Lists.newArrayList();
        }

        return Optional.ofNullable(((JSONObject) resp.getData()))
                .map(o -> o.getJSONArray("resultList"))
                .map(o -> o.toJavaList(LanguageDetailResult.class))
                .orElse(null);
    }


    @Override
    public List<LanguageDetailResult> getLanguageList(LanguageDetailQuery query) {
        try {
            return doGetLanguageList(query);
        } catch (Exception e) {
            log.error("调用tool服务查询多语言,发生异常!error:{}", e.getMessage(), e);
            return Lists.newArrayList();
        }
    }

    @Override
//    @Async
    public void sendSmsMessage(SendSmsCommand command) {
        if (CollectionUtils.isEmpty(command.getMobiles())) {
            log.info("发送的短信手机号列表为空!");
            return;
        }


        SendSmsReqDTO sendSmsReqDTO = toolRemoteAssembler.toSendSmsReq(command);
        String url = toolRemoteConfig.getAllUrl(toolRemoteConfig.getSmsNotify()) + "?country=" + sendSmsReqDTO.getCountryCode();
        log.info("发送短信,url:{},dto:{},req:{}", url, JsonUtil.toJsonString(command), JsonUtil.toJsonString(sendSmsReqDTO));
        BaseResponseVo sendResp = null;
        try {
            log.info("短信发送req={}", JsonUtil.toJsonString(sendSmsReqDTO));
            sendResp = toolHttpClient.post(url, JsonUtil.toJsonString(sendSmsReqDTO));
            log.info("短信发送resp={}", JsonUtil.toJsonString(sendResp));
        } catch (IOException e) {
            log.error("短信发送异常!", e);
            throw new GlobalI18nException(ExceptionEnum.THIRD_SYSTEM_ERROR);
        }

        boolean invokeSuccess = toolRemoteHelper.invokeSuccess(sendResp);
        if (!invokeSuccess) {
            log.error("短信发送,调用服务异常!resp:{}", JsonUtil.toJsonString(sendResp));
            throw new GlobalI18nException(ExceptionEnum.THIRD_SYSTEM_ERROR);
        }
        return;
    }
}
