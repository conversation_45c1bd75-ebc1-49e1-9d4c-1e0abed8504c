package com.qudian.idle.inspection.app.infrastructure.repository;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qudian.idle.inspection.app.infrastructure.repository.database.mapper.TaskPoolMapper;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.TaskPoolPO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class TaskPoolRepository {

    @Resource
    private TaskPoolMapper taskPoolMapper;

    public int save(TaskPoolPO entity) {
        return taskPoolMapper.insert(entity);
    }

    public int update(TaskPoolPO entity) {
        return taskPoolMapper.updateById(entity);
    }

    public TaskPoolPO findById(Long id) {
        return taskPoolMapper.selectById(id);
    }

    public List<TaskPoolPO> listByTaskNo(String taskNo) {
        return taskPoolMapper.selectList(new LambdaQueryWrapper<TaskPoolPO>()
                .eq(TaskPoolPO::getTaskNo, taskNo)
                .eq(TaskPoolPO::getDeleteFlag, 0));
    }

    public List<TaskPoolPO> listByJob(Long jobId, Integer jobLevel) {
        return taskPoolMapper.selectList(new LambdaQueryWrapper<TaskPoolPO>()
                .eq(TaskPoolPO::getJobId, jobId)
                .eq(jobLevel != null, TaskPoolPO::getJobLevel, jobLevel)
                .eq(TaskPoolPO::getDeleteFlag, 0));
    }

    public int softDeleteById(Long id) {
        TaskPoolPO po = new TaskPoolPO();
        po.setId(id);
        po.setDeleteFlag(1);
        return taskPoolMapper.updateById(po);
    }
}