package com.qudian.idle.inspection.app.server.facade;


import com.qudian.idle.inspection.app.api.facade.EchoesOfTheValleyFacade;
import com.qudian.idle.inspection.app.api.vo.request.ValleyEchosReqVO;
import com.qudian.idle.inspection.app.server.StartTestApplication;
import com.qudian.lme.common.dto.BaseResponseDTO;
import com.qudian.pdt.toolkit.dubbo.core.annotation.DubboReference;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

/**
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2024</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> architecture board)
 * @version 1.0
 */
@TestMethodOrder(MethodOrderer.MethodName.class)
@Slf4j
public class ValleyEchoFacadeTest extends StartTestApplication {
    @DubboReference
    private EchoesOfTheValleyFacade valleyEchosFacade;

    @Test
    public void echo() {
        ValleyEchosReqVO reqVO = new ValleyEchosReqVO();
        reqVO.setHeyRoar("revolution");
        BaseResponseDTO<ValleyEchosReqVO> echo = valleyEchosFacade.echo(reqVO);

        ValleyEchosReqVO res = new ValleyEchosReqVO();
        res.setHeyRoar("[Server_echo] hi:" + reqVO.getHeyRoar());
        assertThat(echo.toString()).isNotNull().isEqualTo("{\"code\":0,\"message\":\"SUCCESS\",\"displayable\":false,\"data\":\"ValleyEchosReqVO(heyRoar=[Server_echo] hi:revolution, origin=null)\"}");
    }
}
