package com.qudian.idle.inspection.app.server.testcase;

import com.qudian.idle.inspection.app.api.enums.auth.AuthTypeEnum;
import com.qudian.idle.inspection.app.api.enums.auth.SendMsgTypeEnum;
import com.qudian.idle.inspection.app.api.facade.AuthFacade;
import com.qudian.idle.inspection.app.api.vo.request.auth.LoginBySmsCodeReqVO;
import com.qudian.idle.inspection.app.api.vo.request.auth.SendVerificationCodeReqVO;
import com.qudian.idle.inspection.app.api.vo.response.auth.LoginBySmsCodeRespVO;
import com.qudian.idle.inspection.app.api.vo.response.common.CommandResultRespVO;
import com.qudian.idle.inspection.app.common.utils.json.JsonUtil;
import com.qudian.idle.inspection.app.server.StartTestNoRollBackApplication;
import com.qudian.lme.common.dto.BaseResponseDTO;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;

public class LoginTest extends StartTestNoRollBackApplication {

    @Resource
    private AuthFacade authFacade;

    @Test
    public void testSendSmsCode() {
        SendVerificationCodeReqVO req = new SendVerificationCodeReqVO();
        req.setEmailOrMobile("18059851004");
        req.setAuthType(AuthTypeEnum.MOBILE.getCode());
        req.setSendType(SendMsgTypeEnum.LOGIN.getCode());
        req.setPlatform(null);
        BaseResponseDTO<CommandResultRespVO<Object>> resp = authFacade.sendVerificationCode(req);
        System.out.println(JsonUtil.toJsonString(resp));
    }

    @Test
    public void testLogin() {
        LoginBySmsCodeReqVO req = new LoginBySmsCodeReqVO();
        req.setEmailOrMobile("18059851004");
        req.setAuthType(AuthTypeEnum.MOBILE.getCode());
        //818453
        req.setCode("818453");
        req.setSendType(SendMsgTypeEnum.LOGIN.getCode());
        BaseResponseDTO<LoginBySmsCodeRespVO> resp = authFacade.loginBySmsCode(req);
        System.out.println(JsonUtil.toJsonString(resp));
    }


}
