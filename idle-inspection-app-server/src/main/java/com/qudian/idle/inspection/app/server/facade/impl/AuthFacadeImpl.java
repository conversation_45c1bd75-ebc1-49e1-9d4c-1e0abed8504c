package com.qudian.idle.inspection.app.server.facade.impl;

import com.qudian.idle.inspection.app.api.facade.AuthFacade;
import com.qudian.idle.inspection.app.api.vo.request.auth.AuthenticationReqVO;
import com.qudian.idle.inspection.app.api.vo.request.auth.EmployeeInfoQueryReqVO;
import com.qudian.idle.inspection.app.api.vo.request.auth.InspectionPermissionQueryReqVO;
import com.qudian.idle.inspection.app.api.vo.request.auth.LoginBySmsCodeReqVO;
import com.qudian.idle.inspection.app.api.vo.request.auth.SendVerificationCodeReqVO;
import com.qudian.idle.inspection.app.api.vo.response.auth.AuthenticationRespVO;
import com.qudian.idle.inspection.app.api.vo.response.auth.EmployeeInfoQueryRespVO;
import com.qudian.idle.inspection.app.api.vo.response.auth.InspectionPermissionQueryRespVO;
import com.qudian.idle.inspection.app.api.vo.response.auth.LoginBySmsCodeRespVO;
import com.qudian.idle.inspection.app.api.vo.response.common.CommandResultRespVO;
import com.qudian.idle.inspection.app.business.handler.auth.AuthenticationHandler;
import com.qudian.idle.inspection.app.business.handler.auth.EmployeeInfoQueryHandler;
import com.qudian.idle.inspection.app.business.handler.auth.InspectionPermissionQueryHandler;
import com.qudian.idle.inspection.app.business.handler.auth.LoginHandler;
import com.qudian.idle.inspection.app.business.handler.auth.SendVerificationCodeHandler;
import com.qudian.lme.base.builder.ResponseBuilder;
import com.qudian.lme.common.dto.BaseResponseDTO;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 权限相关接口
 */
@RestController("auth")
@DubboService
public class AuthFacadeImpl implements AuthFacade {

    @Resource
    private SendVerificationCodeHandler sendVerificationCodeHandler;

    @Resource
    private InspectionPermissionQueryHandler inspectionPermissionQueryHandler;

    @Resource
    private LoginHandler loginHandler;

    @Resource
    private EmployeeInfoQueryHandler employeeInfoQueryHandler;

    @Resource
    private AuthenticationHandler authenticationHandler;

    /**
     * 发送验证码
     * @param command
     * @return
     */
    @PostMapping("api/inspection/auth/sendVerificationCode")
    @Override
    public BaseResponseDTO<CommandResultRespVO<Object>> sendVerificationCode(@RequestBody SendVerificationCodeReqVO command) {
        return ResponseBuilder.buildSuccess(sendVerificationCodeHandler.handle(command));
    }


    /**
     * 登陆
     * @param command
     * @return
     */
    @PostMapping("api/inspection/auth/login")
    @Override
    public BaseResponseDTO<LoginBySmsCodeRespVO> loginBySmsCode(@RequestBody LoginBySmsCodeReqVO command) {
        return ResponseBuilder.buildSuccess(loginHandler.loginBySmsCode(command));
    }


    /**
     * 授权
     * @param command
     * @return
     */
    @PostMapping("api/inspection/auth/authentication")
    @Override
    public BaseResponseDTO<AuthenticationRespVO> authentication(@RequestBody AuthenticationReqVO command) {
        return ResponseBuilder.buildSuccess(authenticationHandler.handle(command));
    }

    /**
     * 查询员工信息
     * @param query
     * @return
     */
    @Override
    @PostMapping("api/inspection/auth/employeeInfo")
    public BaseResponseDTO<EmployeeInfoQueryRespVO> queryEmployeeInfo(@RequestBody EmployeeInfoQueryReqVO query) {
        return ResponseBuilder.buildSuccess(employeeInfoQueryHandler.handle(query));
    }

    @Override
    public BaseResponseDTO<InspectionPermissionQueryRespVO> inspectionPermissionQuery(InspectionPermissionQueryReqVO query) {
        return ResponseBuilder.buildSuccess(inspectionPermissionQueryHandler.query(query));
    }
}

