package com.qudian.idle.inspection.app.server.facade.impl.certificate;

import com.qudian.idle.inspection.app.api.facade.certificate.CertificatesFacade;
import com.qudian.idle.inspection.app.api.vo.request.certificate.CertificateBaseShowReqVO;
import com.qudian.idle.inspection.app.api.vo.response.certificate.CertificateBaseShowRespVO;
import com.qudian.lme.common.dto.BaseResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * <p>文件名称:com.qudian.idle.inspection.app.server.facade.impl.certificate.CertificateFacadeImpl</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/21
 */
@DubboService(version = "1.0.0")
@Slf4j
public class CertificateFacadeImpl implements CertificatesFacade {

    @Override
    public BaseResponseDTO<CertificateBaseShowRespVO> show(CertificateBaseShowReqVO reqVO) {
        return null;
    }
}
