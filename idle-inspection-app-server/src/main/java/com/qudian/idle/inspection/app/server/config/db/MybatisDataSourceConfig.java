package com.qudian.idle.inspection.app.server.config.db;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.Optional;
/**
 * <p>文件描述: 数据源主配置</p>
 * <p>版权所有: Copyright(C)2019-2024</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> architecture board)
 * @version 1.0
 */
@Configuration
@MapperScan(basePackages = {MybatisDataSourceConfig.PACKAGE}, sqlSessionFactoryRef = "mybatisSqlSessionFactory")
@EnableTransactionManagement
@Slf4j
public class MybatisDataSourceConfig {
    protected static final String PACKAGE = "com.qudian.idle.inspection.app.infrastructure.repository.database.mapper";

    @Bean(name = "mybatisDataSource")
    @ConfigurationProperties(prefix = "spring.shardingsphere.datasource.master")
    public DataSource mybatisDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "mybatisSqlSessionFactory")
    public SqlSessionFactory permissionsDataSourceSqlSessionFactory(@Qualifier("mybatisDataSource") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
        MybatisPlusInterceptor mybatisPlusInterceptor = new MybatisPlusInterceptor();
        mybatisPlusInterceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        bean.addPlugins(new Interceptor[] {mybatisPlusInterceptor});
        bean.setDataSource(dataSource);
        Optional.ofNullable(bean.getObject()).ifPresent(s -> s.getConfiguration().setMapUnderscoreToCamelCase(true));
        Optional.ofNullable(bean.getObject()).ifPresent(s -> s.getConfiguration().setJdbcTypeForNull(JdbcType.NULL));
        return bean.getObject();
    }

    @Bean(name = "mybatisTransactionManager")
    public DataSourceTransactionManager permissionsTransactionManager(@Qualifier("mybatisDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "mybatisSqlSessionTemplate")
    public SqlSessionTemplate permissionsSqlSessionTemplate(@Qualifier("mybatisSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}
