package com.qudian.idle.inspection.app.server.facade.impl;

import com.qudian.idle.inspection.app.api.facade.TaskFacade;
import com.qudian.idle.inspection.app.api.vo.request.task.*;
import com.qudian.idle.inspection.app.api.vo.response.task.*;
import com.qudian.idle.inspection.app.api.vo.share.PagingList;
import com.qudian.idle.inspection.app.api.vo.share.SuccessRespVO;
import com.qudian.idle.inspection.app.business.service.TaskService;
import com.qudian.lme.base.builder.ResponseBuilder;
import com.qudian.lme.common.dto.BaseResponseDTO;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 任务相关接口实现
 */
@RestController("task")
public class TaskFacadeImpl implements TaskFacade {


    @Resource
    private TaskService taskService;

    /**
     * 任务大厅
     * @param query 查询参数
     * @return 分页任务列表
     */
    @PostMapping("api/task/queryTaskList")
    @Override
    public BaseResponseDTO<PagingList<TaskListItemRespVO>> queryTaskList(@RequestBody TaskListQueryReqVO query) {
        PagingList<TaskListItemRespVO> taskListItemRespVOPagingList = taskService.queryTaskList(query);
        return ResponseBuilder.buildSuccess(taskListItemRespVOPagingList);
    }

    /**
     * 认领任务接口
     * @param command 认领参数
     * @return 认领结果
     */
    @PostMapping("api/task/claimTask")
    @Override
    public BaseResponseDTO<SuccessRespVO> claimTask(@RequestBody TaskClaimReqVO command) {
        // TODO 接入认领服务并封装返回
        return ResponseBuilder.buildSuccess(taskService.claimTask(command));
    }

    /**
     * 我的认领任务列表
     * @param query 查询参数
     * @return 分页任务列表
     */
    @PostMapping("api/task/queryClaimedTaskList")
    @Override
    public BaseResponseDTO<PagingList<ClaimedTaskListItemRespVO>> queryClaimedTaskList(@RequestBody TaskListQueryReqVO query) {
        // TODO 接入我的任务查询服务并封装返回
        return null;
    }


    /**
     * 扫码商品查询可执行的子任务列表
     * @param query 查询参数
     * @return 子任务列表
     */
    @PostMapping("api/task/scan")
    @Override
    public BaseResponseDTO<TaskSubListRespVO> queryTaskSubList(@RequestBody TaskSubListQueryReqVO query) {
        // TODO 接入详情查询服务并封装返回
        return null;
    }

    /**
     * 查询子任务详情
     * @param query 查询参数
     * @return 子任务详情
     */
    @Override
    @PostMapping("api/task/queryTaskSubDetail")
    public BaseResponseDTO<TaskSubDetailRespVO> queryTaskSubDetail(TaskSubListQueryReqVO query) {
        return null;
    }

    /**
     * 开始子任务
     * @param command 开始参数（包含子任务标识、执行人等）
     * @return 操作结果
     */
    @Override
    @PostMapping("api/task/startTaskSub")
    public BaseResponseDTO<SuccessRespVO> startTaskSub(TaskSubStartReqVO command) {
        return null;
    }

    /**
     * 提交子任务
     * @param command 提交参数（包含子任务输出数据）
     * @return 操作结果
     */
    @Override
    @PostMapping("api/task/submitTaskSub")
    public BaseResponseDTO<SuccessRespVO> submitTaskSub(TaskSubSubmitReqVO command) {
        // 校验任务人
        return null;
    }

    /**
     * 挂起与恢复子任务（状态流转）
     * 注意：当前方法未标注路由映射，需补充 \@PostMapping("api/task/changeStatus") 后对外暴露
     * @param command 状态变更参数
     * @return 操作结果
     */
    @Override
    public BaseResponseDTO<SuccessRespVO> changeStatus(TaskSubChangeStatusReqVO command) {
        return null;
    }

    /**
     * 查询用户进行中的任务数
     * @param query 查询参数（执行人、品牌、分类等）
     * @return 进行中任务数量
     */
    @Override
    @PostMapping("api/task/queryDoingCount")
    public BaseResponseDTO<TaskCountRespVO> queryDoingCount(TaskCountReqVO query) {
        return null;
    }
}