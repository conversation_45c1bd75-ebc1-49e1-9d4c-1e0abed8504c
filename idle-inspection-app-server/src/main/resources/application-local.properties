logging.config=classpath:log4j2-spring-local.xml
logging.level.com.qudian.wanwu.shop.tripod.dao=debug


spring.shardingsphere.datasource.master.url = ***************************************************************************************************************************************************************************************************************************************************************
spring.shardingsphere.datasource.master.jdbc-url = ***************************************************************************************************************************************************************************************************************************************************************
spring.shardingsphere.datasource.slave0.url = ***************************************************************************************************************************************************************************************************************************************************************
spring.shardingsphere.datasource.names = master,slave0
spring.shardingsphere.datasource.master.type = com.zaxxer.hikari.HikariDataSource
spring.shardingsphere.datasource.master.driver-class-name = com.mysql.cj.jdbc.Driver
spring.shardingsphere.datasource.master.username = qdfresh_test
spring.shardingsphere.datasource.master.password = ZYF5gThaUNdf4QV6
spring.shardingsphere.datasource.master.initialSize = 5
spring.shardingsphere.datasource.master.maxActive = 30
spring.shardingsphere.datasource.master.minIdle = 5
spring.shardingsphere.datasource.master.maxWait = 3000
spring.shardingsphere.datasource.master.validationQuery = SELECT 1
spring.shardingsphere.datasource.master.validationQueryTimeout = 30
spring.shardingsphere.datasource.master.testOnBorrow = false
spring.shardingsphere.datasource.master.testOnReturn = false
spring.shardingsphere.datasource.master.testWhileIdle = true
spring.shardingsphere.datasource.master.timeBetweenEvictionRunsMillis = 60000
spring.shardingsphere.datasource.master.minEvictableIdleTimeMillis = 300000
spring.shardingsphere.datasource.master.connectionInitSqls = set names utf8mb4
spring.shardingsphere.datasource.master.filters = stat,log4j,wall
spring.shardingsphere.datasource.slave0.type = com.zaxxer.hikari.HikariDataSource
spring.shardingsphere.datasource.slave0.driver-class-name = com.mysql.cj.jdbc.Driver
spring.shardingsphere.datasource.slave0.username = qdfresh_test
spring.shardingsphere.datasource.slave0.password = ZYF5gThaUNdf4QV6
spring.shardingsphere.datasource.slave0.initialSize = 15
spring.shardingsphere.datasource.slave0.maxActive = 50
spring.shardingsphere.datasource.slave0.minIdle = 30
spring.shardingsphere.datasource.slave0.maxWait = 3000
spring.shardingsphere.datasource.slave0.validationQuery = SELECT 1
spring.shardingsphere.datasource.slave0.validationQueryTimeout = 30
spring.shardingsphere.datasource.slave0.testOnBorrow = false
spring.shardingsphere.datasource.slave0.testOnReturn = false
spring.shardingsphere.datasource.slave0.testWhileIdle = true
spring.shardingsphere.datasource.slave0.timeBetweenEvictionRunsMillis = 60000
spring.shardingsphere.datasource.slave0.minEvictableIdleTimeMillis = 300000
spring.shardingsphere.datasource.slave0.connectionInitSqls = set names utf8mb4
spring.shardingsphere.datasource.slave0.filters = stat,log4j,wall
spring.shardingsphere.masterslave.load-balance-algorithm-type = round_robin
spring.shardingsphere.masterslave.name = ms
spring.shardingsphere.masterslave.master-data-source-name = master
spring.shardingsphere.masterslave.slave-data-source-names = slave0
spring.shardingsphere.props.sql.show = true
spring.data.redis.host = lme-dev1-o.redis.zhangbei.rds.aliyuncs.com
spring.data.redis.block-when-exhausted = false
spring.data.redis.database = 12
spring.data.redis.port = 6379
spring.data.redis.jedis.pool.max-active = 200
spring.data.redis.jedis.pool.max-wait = 10000
spring.data.redis.jedis.pool.max-idle = 8
spring.data.redis.jedis.pool.min-idle = 0
spring.data.redis.timeout = 10000
spring.cache.redis.time-to-live = 600
spring.data.redis.password = JQgFNrilA86vGRjh

dubbo.protocol.dubbo.payload = 8388608
dubbo.application.name = ${spring.application.name}
dubbo.registry.address = zookeeper://127.1:2181
dubbo.registry.timeout = 6000
dubbo.metadata-report.address = zookeeper://127.1:2181
dubbo.protocol.name = dubbo
dubbo.protocol.port = 34066
dubbo.scan.base-packages = com.qudian.*
dubbo.reference.check = false
dubbo.consumer.check = false
dubbo.consumer.timeout = 5000
dubbo.consumer.retries = 0
dubbo.registry.check = false
dubbo.provider.threads = 400
dubbo.provider.retries = 0
dubbo.provider.version = 1.0.0
dubbo.reference.default.version = 1.0.0
dubbo.provider.validation = jvalidationNew
dubbo.counsumer.validation = jvalidationNew
#dubbo.provider.filter = -exception,-validation,generic,dubboExceptionFilter,dubboValidationFilter,default,dubboI18nFilter



remote.tool.host = http://lme-dev-tool-srv.quplusplus.net
remote.tool.storeProvider=oss

sms.business=idle_validation