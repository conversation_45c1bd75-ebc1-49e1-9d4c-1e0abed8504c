#!/bin/sh
mkdir -p /data/idle/idle-inspection-app-server/`hostname`/   /data/logs/idle/
ln -s /data/idle/idle-inspection-app-server/`hostname`/ /data/logs/idle/idle-inspection-app-server

trap_handler() {
  curl "localhost:22222/offline"
  sleep 15s
  echo '准备kill java进程'
  ps -ef | grep java | grep idle-inspection-app-server | awk '{print $2}' | xargs kill
}

trap 'trap_handler' 15

java -Dfile.encoding=UTF-8 $JAVA_OPTS -javaagent:/usr/local/skywalking/agent/skywalking-agent.jar=$SW_PARAMS -jar /usr/local/$JAR_FILE &

while true
do
  sleep 5
  pidof java||exit 1
done
