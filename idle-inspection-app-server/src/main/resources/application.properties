#spring
spring.application.name=idle-inspection-app-server
server.servlet.context-path=/api
spring.messages.encoding=UTF-8
spring.profiles.active=@profilesActive@
spring.main.allow-bean-definition-overriding=true
spring.main.allow-circular-references=true
server.port=8080
#dubbo
dubbo.protocol.accesslog=true
#dubbo.application.logger=log4j2
#apollo
app.id=idle-inspection-app-server
management.endpoints.web.exposure.include=info,health
