#基础镜像，如果本地仓库没有，会从远程仓库拉取
FROM newhub.fadongxi.com/library/openjdk17-skywalking9
#虚拟机内存变量
ENV JAVA_OPTS=$JAVA_OPTS
#skywalking应用名及服务地址配置。（-e SW_PARAMS=agent.service_name=xxx,collector.backend_service=ip:port）
ENV SW_PARAMS=$SW_PARAMS
ARG JAR_FILE
ENV JAR_FILE=$JAR_FILE
#容器中创建目录
RUN mkdir -p /usr/local
#编译后的jar包copy到容器中创建到目录内
COPY /target/$JAR_FILE /usr/local/$JAR_FILE
COPY src/main/resources/entrypoint.sh /usr/local/entrypoint.sh
RUN chmod +x /usr/local/entrypoint.sh
WORKDIR /usr/local
ENTRYPOINT ["/usr/local/entrypoint.sh"]
