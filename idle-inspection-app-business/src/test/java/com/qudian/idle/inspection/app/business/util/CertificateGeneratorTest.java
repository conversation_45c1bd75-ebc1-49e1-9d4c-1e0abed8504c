package com.qudian.idle.inspection.app.business.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.RepeatedTest;
import org.junit.jupiter.api.DisplayName;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 证书编号生成器测试类
 * 
 * <AUTHOR>
 * @since 2025/8/22
 */
class CertificateGeneratorTest {

    @Test
    @DisplayName("测试证书编号生成格式")
    void testGenerateCertificateNoFormat() {
        String certificateNo = CertificateGenerator.generateCertificateNo();
        
        // 验证长度为14位
        assertEquals(14, certificateNo.length(), "证书编号应为14位");
        
        // 验证全为数字
        assertTrue(certificateNo.matches("\\d{14}"), "证书编号应全为数字");
        
        // 验证业务类型标识
        assertTrue(certificateNo.startsWith("01"), "证书编号应以01开头（奢侈品鉴别）");
        
        System.out.println("Generated certificate number: " + certificateNo);
    }

    @Test
    @DisplayName("测试防伪扣编号生成格式")
    void testGenerateAntiCounterfeitNoFormat() {
        String antiCounterfeitNo = CertificateGenerator.generateAntiCounterfeitNo();
        
        // 验证长度为12位
        assertEquals(12, antiCounterfeitNo.length(), "防伪扣编号应为12位");
        
        // 验证全为数字
        assertTrue(antiCounterfeitNo.matches("\\d{12}"), "防伪扣编号应全为数字");
        
        System.out.println("Generated anti-counterfeit number: " + antiCounterfeitNo);
    }

    @Test
    @DisplayName("测试证书编号验证功能")
    void testValidateCertificateNo() {
        // 生成有效的证书编号
        String validCertificateNo = CertificateGenerator.generateCertificateNo();
        assertTrue(CertificateGenerator.validateCertificateNo(validCertificateNo), 
                "生成的证书编号应通过验证");
        
        // 测试无效的证书编号
        assertFalse(CertificateGenerator.validateCertificateNo(null), "null应验证失败");
        assertFalse(CertificateGenerator.validateCertificateNo(""), "空字符串应验证失败");
        assertFalse(CertificateGenerator.validateCertificateNo("123"), "长度不足应验证失败");
        assertFalse(CertificateGenerator.validateCertificateNo("12345678901234567"), "长度过长应验证失败");
        assertFalse(CertificateGenerator.validateCertificateNo("1234567890123a"), "包含字母应验证失败");
        assertFalse(CertificateGenerator.validateCertificateNo("02250822143012"), "错误的业务类型标识应验证失败");
        
        // 测试校验位错误的情况
        String invalidCheckDigit = validCertificateNo.substring(0, 13) + "9";
        if (!invalidCheckDigit.equals(validCertificateNo)) {
            assertFalse(CertificateGenerator.validateCertificateNo(invalidCheckDigit), 
                    "错误的校验位应验证失败");
        }
    }

    @Test
    @DisplayName("测试时间戳解析功能")
    void testParseTimestamp() {
        String certificateNo = CertificateGenerator.generateCertificateNo();
        String timestamp = CertificateGenerator.parseTimestamp(certificateNo);
        
        assertNotNull(timestamp, "时间戳不应为null");
        assertTrue(timestamp.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}"), 
                "时间戳格式应为 yyyy-MM-dd HH:mm");
        
        System.out.println("Certificate number: " + certificateNo);
        System.out.println("Parsed timestamp: " + timestamp);
        
        // 测试无效证书编号的时间戳解析
        assertThrows(IllegalArgumentException.class, 
                () -> CertificateGenerator.parseTimestamp("12345678901234"), 
                "无效证书编号应抛出异常");
    }

    @RepeatedTest(100)
    @DisplayName("测试证书编号唯一性")
    void testCertificateNoUniqueness() {
        Set<String> generatedNumbers = new HashSet<>();
        
        for (int i = 0; i < 1000; i++) {
            String certificateNo = CertificateGenerator.generateCertificateNo();
            assertFalse(generatedNumbers.contains(certificateNo), 
                    "证书编号应保持唯一性: " + certificateNo);
            generatedNumbers.add(certificateNo);
        }
    }

    @Test
    @DisplayName("测试并发生成证书编号的唯一性")
    void testConcurrentUniqueness() throws InterruptedException {
        int threadCount = 10;
        int numbersPerThread = 100;
        Set<String> allNumbers = ConcurrentHashMap.newKeySet();
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < numbersPerThread; j++) {
                        String certificateNo = CertificateGenerator.generateCertificateNo();
                        assertTrue(allNumbers.add(certificateNo), 
                                "并发生成的证书编号应保持唯一性: " + certificateNo);
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await();
        executor.shutdown();
        
        assertEquals(threadCount * numbersPerThread, allNumbers.size(), 
                "所有生成的证书编号都应该是唯一的");
    }

    @Test
    @DisplayName("测试证书编号的业务含义")
    void testBusinessMeaning() {
        String certificateNo = CertificateGenerator.generateCertificateNo();
        
        // 验证业务类型标识
        String businessType = certificateNo.substring(0, 2);
        assertEquals("01", businessType, "业务类型应为01（奢侈品鉴别）");
        
        // 验证时间戳部分
        String timestampPart = certificateNo.substring(2, 10);
        assertTrue(timestampPart.matches("\\d{8}"), "时间戳部分应为8位数字");
        
        // 验证随机数部分
        String randomPart = certificateNo.substring(10, 13);
        assertTrue(randomPart.matches("\\d{3}"), "随机数部分应为3位数字");
        
        // 验证校验位
        String checkDigit = certificateNo.substring(13);
        assertTrue(checkDigit.matches("\\d{1}"), "校验位应为1位数字");
        
        System.out.println("Certificate number breakdown:");
        System.out.println("  Full number: " + certificateNo);
        System.out.println("  Business type: " + businessType);
        System.out.println("  Timestamp: " + timestampPart);
        System.out.println("  Random part: " + randomPart);
        System.out.println("  Check digit: " + checkDigit);
    }

    @Test
    @DisplayName("测试防伪扣编号的时间含义")
    void testAntiCounterfeitNoTimeMeaning() {
        String antiCounterfeitNo = CertificateGenerator.generateAntiCounterfeitNo();
        
        // 验证年月日部分
        String datePart = antiCounterfeitNo.substring(0, 6);
        assertTrue(datePart.matches("\\d{6}"), "年月日部分应为6位数字");
        
        // 验证时分秒部分
        String timePart = antiCounterfeitNo.substring(6, 12);
        assertTrue(timePart.matches("\\d{6}"), "时分秒部分应为6位数字");
        
        System.out.println("Anti-counterfeit number breakdown:");
        System.out.println("  Full number: " + antiCounterfeitNo);
        System.out.println("  Date part (YYMMDD): " + datePart);
        System.out.println("  Time part (HHMMSS): " + timePart);
    }
}
