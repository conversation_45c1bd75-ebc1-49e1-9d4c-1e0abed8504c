package com.qudian.idle.inspection.app.business.handler.auth;

import cn.hutool.core.lang.Assert;
import com.qudian.idle.inspection.app.api.enums.auth.AuthTypeEnum;
import com.qudian.idle.inspection.app.api.enums.auth.PlatformEnum;
import com.qudian.idle.inspection.app.api.enums.auth.SendMsgTypeEnum;
import com.qudian.idle.inspection.app.api.enums.employee.EmployeeSourceSystemEnum;
import com.qudian.idle.inspection.app.api.vo.request.auth.LoginBySmsCodeReqVO;
import com.qudian.idle.inspection.app.api.vo.request.auth.UserTokenReqVO;
import com.qudian.idle.inspection.app.api.vo.response.auth.LoginBySmsCodeRespVO;
import com.qudian.idle.inspection.app.api.vo.response.common.CommandResultRespVO;
import com.qudian.idle.inspection.app.business.helper.AuthHelper;
import com.qudian.idle.inspection.app.common.config.SystemConfig;
import com.qudian.idle.inspection.app.common.enums.BizErrorEnum;
import com.qudian.idle.inspection.app.common.utils.json.JsonUtil;
import com.qudian.idle.inspection.app.infrastructure.handler.LanguageHandler;
import com.qudian.idle.inspection.app.infrastructure.helper.CommonHelper;
import com.qudian.idle.inspection.app.infrastructure.repository.EmployeeRepository;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.EmployeePO;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.internal.tool.PhoneCheckQuery;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.internal.tool.PhoneCheckResult;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.remote.ToolRemote;
import com.qudian.pdt.toolkit.common.enums.ExceptionEnum;
import com.qudian.pdt.toolkit.common.exception.GlobalI18nException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class LoginHandler {

    @Resource
    private EmployeeRepository employeeRepository;

    @Resource
    private SystemConfig systemConfig;

    @Resource
    private ToolRemote toolRemote;

    @Resource
    private CommonHelper commonHelper;

    @Resource
    private AuthHelper authHelper;

    @Resource
    private LanguageHandler languageHandler;

    public LoginBySmsCodeRespVO loginBySmsCode(LoginBySmsCodeReqVO command) {
        checkCommand(command);
        fillDefaultCommand(command);
        PhoneCheckResult phoneCheckResult = checkPhone(command);
        if (!phoneCheckResult.getCheckResult()) {
            log.info("校验手机号失败!校验的手机号:{},checkResult:{}", command.getEmailOrMobile(), JsonUtil.toJsonString(phoneCheckResult));
            return buildErrorResult(BizErrorEnum.USER_MOBILE_FORMAT_INCORRECT);
        }

        Assert.notBlank(phoneCheckResult.getRightPhone());
        //格式化手机号码
        command.setEmailOrMobile(phoneCheckResult.getRightPhone());

        String mobile = command.getEmailOrMobile();
        EmployeePO employee = employeeRepository.selectByMobile(mobile);
        if (employee == null) {
            log.info("登陆时，根据手机号:{}查询员工，查询为空!", mobile);
            return buildErrorResult(BizErrorEnum.MOBILE_NOT_SIGNING);
        }

        if (command.getSourceSystem() == null) {
            command.setSourceSystem(employee.getSourceSystem());
        }

        Pair<Boolean, String> checkResult = checkMobileVerificationCode(command, employee);
        if (checkResult != null && !checkResult.getLeft()) {
            log.info("登陆时，验证码错误!code:{}", command.getCode());
            return buildErrorResult(BizErrorEnum.SMS_VERIFICATION_CODE);
        }

        String token = checkResult.getRight();
        UserTokenReqVO userTokenReqVO = UserTokenReqVO.builder().userId(employee.getId()).build();
        token = authHelper.getToken(userTokenReqVO);
        LoginBySmsCodeRespVO result = new LoginBySmsCodeRespVO();
        result.setToken(token);
        result.setUserId(employee.getId());
        result.setSuccess(true);
        result.setSourceSystem(employee.getSourceSystem());
        return result;
    }

    private LoginBySmsCodeRespVO buildErrorResult(BizErrorEnum bizErrorEnum) {
        LoginBySmsCodeRespVO result = new LoginBySmsCodeRespVO();
        if (bizErrorEnum == null) {
            bizErrorEnum = BizErrorEnum.SYSTEM_ERR;
        }
        String failMessage = languageHandler.fetchFromAppLanguage(bizErrorEnum.getI18nKey(), bizErrorEnum.getMsg());
        result.setSuccess(false);
        result.setFailedMessage(failMessage);
        return result;
    }

    private void fillDefaultCommand(LoginBySmsCodeReqVO command) {
        if (StringUtils.isBlank(command.getPlatform())) {
            command.setPlatform(PlatformEnum.APP.getCode());
        }
    }

    private Pair<Boolean,String> checkMobileVerificationCode(LoginBySmsCodeReqVO command, EmployeePO employee) {
        return authHelper.checkMobileVerificationCode(command.getEmailOrMobile(), command.getCode(), SendMsgTypeEnum.LOGIN, command.getPlatform(), command.getSourceSystem());
    }

    private PhoneCheckResult checkPhone(LoginBySmsCodeReqVO command) {
        String mobile = command.getEmailOrMobile();
        PhoneCheckQuery phoneCheckQuery = new PhoneCheckQuery();
        phoneCheckQuery.setPhone(mobile);
        phoneCheckQuery.setCountryCode(systemConfig.getDefaultCountryCode());
        PhoneCheckResult phoneCheckResult = toolRemote.checkPhone(phoneCheckQuery);
        return phoneCheckResult;
    }

    private void checkCommand(LoginBySmsCodeReqVO command) {
        Assert.notBlank(command.getAuthType());
        Assert.notBlank(command.getEmailOrMobile());
        Assert.notBlank(command.getSendType());
        AuthTypeEnum authTypeEnum = AuthTypeEnum.getByCode(command.getAuthType());
        if (authTypeEnum == null) {
            log.error("不支持的登录方式!authType:{}", command.getAuthType());
            throw new GlobalI18nException(ExceptionEnum.PARAMETER_VERIFICATION, BizErrorEnum.SEND_MSG_TYPE_ERROR.getI18nKey());
        }

        String code = command.getCode();
        if (StringUtils.isBlank(code)) {
            log.error("验证码为空!code;{}", code);
            throw new GlobalI18nException(ExceptionEnum.PARAMETER_VERIFICATION, BizErrorEnum.SMS_VERIFICATION_CODE.getI18nKey());
        }
    }
}
