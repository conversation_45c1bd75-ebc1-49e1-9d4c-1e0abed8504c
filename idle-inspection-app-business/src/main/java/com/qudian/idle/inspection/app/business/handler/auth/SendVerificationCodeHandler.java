package com.qudian.idle.inspection.app.business.handler.auth;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import com.google.common.collect.Lists;
import com.qudian.idle.inspection.app.api.enums.auth.AuthTypeEnum;
import com.qudian.idle.inspection.app.api.enums.auth.PlatformEnum;
import com.qudian.idle.inspection.app.api.enums.auth.SendMsgTypeEnum;
import com.qudian.idle.inspection.app.api.enums.employee.EmployeeSourceSystemEnum;
import com.qudian.idle.inspection.app.api.vo.request.auth.SendVerificationCodeReqVO;
import com.qudian.idle.inspection.app.api.vo.response.common.CommandResultRespVO;
import com.qudian.idle.inspection.app.business.helper.AuthHelper;
import com.qudian.idle.inspection.app.common.config.SmsConfig;
import com.qudian.idle.inspection.app.common.config.SystemConfig;
import com.qudian.idle.inspection.app.common.enums.BizErrorEnum;
import com.qudian.idle.inspection.app.common.enums.I18nMessageEnum;
import com.qudian.idle.inspection.app.common.enums.SmsOperatorNameEnum;
import com.qudian.idle.inspection.app.common.enums.SmsSendTypeEnum;
import com.qudian.idle.inspection.app.common.enums.TimeZoneEnum;
import com.qudian.idle.inspection.app.common.utils.common.KaptchaUtil;
import com.qudian.idle.inspection.app.common.utils.common.TimeZoneUtil;
import com.qudian.idle.inspection.app.common.utils.json.JsonUtil;
import com.qudian.idle.inspection.app.common.utils.middleware.RedisUtil;
import com.qudian.idle.inspection.app.infrastructure.handler.LanguageHandler;
import com.qudian.idle.inspection.app.infrastructure.helper.CommonHelper;
import com.qudian.idle.inspection.app.infrastructure.repository.EmployeeRepository;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.EmployeePO;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.internal.tool.PhoneCheckQuery;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.internal.tool.PhoneCheckResult;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.dto.internal.tool.SendSmsCommand;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.remote.ToolRemote;
import com.qudian.pdt.toolkit.common.enums.ExceptionEnum;
import com.qudian.pdt.toolkit.common.exception.GlobalI18nException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Objects;
import java.util.UUID;

@Component
@Slf4j
public class SendVerificationCodeHandler {

    @Resource
    private EmployeeRepository employeeRepository;

    @Resource
    private ToolRemote toolRemote;

    @Resource
    private SystemConfig systemConfig;

    @Resource
    private CommonHelper commonHelper;

    @Resource
    private SmsConfig smsConfig;

    @Resource
    private KaptchaUtil kaptchaUtil;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private LanguageHandler languageHandler;

    @Resource
    private AuthHelper authHelper;

    public CommandResultRespVO<Object> handle(SendVerificationCodeReqVO command) {
        checkCommand(command);
        fillDefaultCommand(command);
        PhoneCheckResult phoneCheckResult = checkPhone(command);
        if (!phoneCheckResult.getCheckResult()) {
            log.info("校验手机号失败!校验的手机号:{},checkResult:{}", command.getEmailOrMobile(), JsonUtil.toJsonString(phoneCheckResult));
            return commonHelper.buildCommandErrorResult(BizErrorEnum.USER_MOBILE_FORMAT_INCORRECT);
        }

        Assert.notBlank(phoneCheckResult.getRightPhone());
        //格式化手机号码
        command.setEmailOrMobile(phoneCheckResult.getRightPhone());
        String mobile = command.getEmailOrMobile();
        //校验手机号是否存在
        EmployeePO employee = employeeRepository.selectByMobile(mobile);
        if (employee == null) {
            log.info("手机号:{}未注册!", command.getEmailOrMobile());
            return commonHelper.buildCommandErrorResult(BizErrorEnum.MOBILE_NOT_SIGNING);
        }


        Integer systemSource = command.getSourceSystem();
        if (systemSource == null) {
            command.setSourceSystem(employee.getSourceSystem());
        }

        SendMsgTypeEnum sendMsgTypeEnum = SendMsgTypeEnum.getByCode(command.getSendType());
        // 发送频率校验
        String smsCommonKey = buildSmsKey(command, employee);

        String frequencyKey = smsCommonKey + "_FREQUENCY";

        String limitKey = smsCommonKey + "_LIMIT";
        CommandResultRespVO checkLimitResult = checkSmsLimit(sendMsgTypeEnum, frequencyKey, limitKey);
        if (checkLimitResult!=null && !checkLimitResult.getSuccess()) {
            log.error("发送短信超过限制!checkResult:{}",JsonUtil.toJsonString(checkLimitResult));
            return checkLimitResult;
        }

        String smsKey = smsCommonKey;

        if (smsConfig.isAuditMobile(mobile)) {
            kaptchaUtil.cacheTextCode(smsKey, smsConfig.getAuditDefaultCode());
            redisUtil.setex(frequencyKey, frequencyKey, 30);
            return CommandResultRespVO.buildSuccess(null);
        }

        //设置高频标识
        redisUtil.setex(frequencyKey, frequencyKey, 30);
        //生成验证码
        String smsCode = kaptchaUtil.createTextCode(smsKey);
        String smsContent = buildSmsContent(smsCode, command, employee);
        toolRemote.sendSmsMessage(SendSmsCommand.builder()
                .mobiles(Lists.newArrayList(mobile))
                .countryCode(systemConfig.getDefaultCountryCode())
                .content(smsContent)
                .business(smsConfig.getBusiness())
                .type(SmsSendTypeEnum.VERIFICATION_CODE.getCode())
                .operatorName(SmsOperatorNameEnum.INSPECTION.getCode())
                .uuid(String.valueOf(UUID.randomUUID()))
                .async(Boolean.TRUE)
                .build());
        return commonHelper.buildCommandSuccessResult(null);
    }

    private void fillDefaultCommand(SendVerificationCodeReqVO command) {
        String platform = command.getPlatform();
        if (StringUtils.isBlank(platform)) {
            command.setPlatform(PlatformEnum.APP.getCode());
        }
    }

    private CommandResultRespVO checkSmsLimit(SendMsgTypeEnum sendMsgTypeEnum, String frequencyKey, String limitKey) {
        //分析短信类型
        if (null == sendMsgTypeEnum) {
            return commonHelper.buildCommandErrorResult(BizErrorEnum.SEND_MSG_TYPE_ERROR);
        }
        //频率限制
        if (StringUtils.isNotBlank(redisUtil.get(frequencyKey, 0))) {
            log.warn("短信发送频率过高，请稍后重试");
            return commonHelper.buildCommandErrorResult(BizErrorEnum.HIGH_QUANTITY_SMS_REQUEST);
        }
        //次数限制
        TimeZoneEnum timeZoneEnum = TimeZoneUtil.getRpcTimeZone();
        // 如果没有传时区过来 去系统默认的时区
        LocalDateTime receiveTimeLocal;
        if (timeZoneEnum == null) {
            receiveTimeLocal = LocalDateTime.now();
        } else {
            receiveTimeLocal = TimeZoneEnum.getCustomizeLocalDateTime(LocalDateTime.now(), timeZoneEnum);
        }
        String limitCountStr = redisUtil.get(limitKey, 0);
        Long limitCount;
        if (StringUtils.isEmpty(limitCountStr)) {
            limitCount = smsConfig.getSmsLimitCount();
            Date currentDateTIme = DateTime.from(receiveTimeLocal.atZone(ZoneId.systemDefault()).toInstant());
            redisUtil.setex(limitKey, Objects.toString(limitCount - 1), (int) DateUtil.between(DateUtil.endOfDay(currentDateTIme), currentDateTIme, DateUnit.SECOND));
        } else {
            limitCount = redisUtil.decr(limitKey);
        }
        if (limitCount < 0) {
            log.warn("验证码发送次数已超过当前的配额");
            return commonHelper.buildCommandErrorResult(BizErrorEnum.LIMIT_QUANTITY_SMS_REQUEST);
        }
        return commonHelper.buildCommandSuccessResult(null);
    }

    private String buildSmsContent(String smsCode, SendVerificationCodeReqVO command, EmployeePO employee) {
        String smsContent = languageHandler.fetchFromMisRemoteForSms(I18nMessageEnum.LOGIN_SMS_CONTENT.getCode(), systemConfig.getDefaultCountryCode(), I18nMessageEnum.LOGIN_SMS_CONTENT.getDefaultContent());
        return String.format(smsContent, smsCode);
    }

    private String buildSmsKey(SendVerificationCodeReqVO command, EmployeePO employee) {
        SendMsgTypeEnum sendMsgTypeEnum = SendMsgTypeEnum.getByCode(command.getSendType());

        return authHelper.buildSmsKey(command.getEmailOrMobile(), command.getPlatform(), sendMsgTypeEnum,command.getSourceSystem());

    }

    private PhoneCheckResult checkPhone(SendVerificationCodeReqVO command) {
        String mobile = command.getEmailOrMobile();
        PhoneCheckQuery phoneCheckQuery = new PhoneCheckQuery();
        phoneCheckQuery.setPhone(mobile);
        phoneCheckQuery.setCountryCode(systemConfig.getDefaultCountryCode());
        PhoneCheckResult phoneCheckResult = toolRemote.checkPhone(phoneCheckQuery);
        return phoneCheckResult;
    }

    private void checkCommand(SendVerificationCodeReqVO command) {
        String authType = command.getAuthType();
        AuthTypeEnum authTypeEnum = AuthTypeEnum.getByCode(authType);
        if (authTypeEnum == null) {
            log.error("不支持的登录方式,authType:{}", command.getAuthType());
            throw new GlobalI18nException(ExceptionEnum.PARAMETER_VERIFICATION);
        }
        Assert.notBlank(command.getEmailOrMobile());
        Assert.notNull(command.getSendType());
    }
}
