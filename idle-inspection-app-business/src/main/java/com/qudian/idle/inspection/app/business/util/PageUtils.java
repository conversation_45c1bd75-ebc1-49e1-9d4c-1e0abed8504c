package com.qudian.idle.inspection.app.business.util;


import com.github.pagehelper.ISelect;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.qudian.idle.inspection.app.api.vo.share.PagingList;
import com.qudian.pdt.api.toolkit.vo.request.PageRequestVO;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.List;

public class PageUtils {

    public static Pageable pageJPA(PageRequestVO page) {
        Pageable pageable = PageRequest.of(page.getPageNum() - 1, page.getPageSize());
        return pageable;
    }

    public static <E> Page<E> pageMybatis(PageRequestVO page) {
        return PageHelper.startPage(page.getPageNum(), page.getPageSize());
    }

    public static <T> PageInfo<T> getPageInfo(PageRequestVO page, ISelect select) {
        return pageMybatis(page).doSelectPageInfo(select);
    }

    public static <T> PagingList getPagingList(PageRequestVO page, ISelect select) {
        return getPagingList(getPageInfo(page, select));
    }

    public static <T> PagingList getPagingList(PageInfo<T> page) {
        PagingList pagingList = new PagingList();
        pagingList.setTotal(page.getTotal());
        pagingList.setPageSize(page.getPageSize());
        pagingList.setPageNum(page.getPageNum());
        pagingList.setHasMore(page.isHasNextPage());
        pagingList.setResultList(page.getList());
        return pagingList;
    }

    public static <T> PagingList getPagingList(Page page, List<T> result) {
        PagingList pagingList = new PagingList();
        pagingList.setTotal(page.getTotal());
        pagingList.setPageSize(page.getPageSize());
        pagingList.setPageNum(page.getPageNum());
        pagingList.setResultList(result);
        pagingList.setHasMore(page.getPageNum() * page.getPageSize() < page.getTotal());
        return pagingList;
    }

    public static <T> PagingList<T> getPagingList(org.springframework.data.domain.Page page, List<T> result) {
        PagingList<T> pagingList = new PagingList<>();
        pagingList.setTotal(page.getTotalElements());
        pagingList.setPageSize(page.getSize());
        pagingList.setPageNum(page.getNumber() + 1);
        pagingList.setResultList(result);
        pagingList.setHasMore(page.hasNext());
        return pagingList;
    }

    public static <T> PagingList getPagingList(Page<T> page) {
        return getPagingList(page, page.getResult());
    }

}
