package com.qudian.idle.inspection.app.business.service.command;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>文件名称:com.qudian.idle.inspection.app.business.service.command.CertificateGeneratesCmd</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class CertificateGeneratesCmd {
    private Integer verdict; //鉴定结果 1-未通过 2-通过
    private String sn;  //商品sn码
    private String orderNo;  //订单号
    private String skuId;   //SKU_ID
}
