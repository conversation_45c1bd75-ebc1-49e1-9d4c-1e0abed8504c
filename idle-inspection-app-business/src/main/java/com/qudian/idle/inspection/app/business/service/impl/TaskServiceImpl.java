// java
package com.qudian.idle.inspection.app.business.service.impl;

import com.github.pagehelper.PageInfo;
import com.qudian.idle.inspection.app.api.vo.request.auth.InspectionPermissionQueryReqVO;
import com.qudian.idle.inspection.app.api.vo.request.task.*;
import com.qudian.idle.inspection.app.api.vo.response.auth.InspectionPermissionQueryRespVO;
import com.qudian.idle.inspection.app.api.vo.response.task.*;
import com.qudian.idle.inspection.app.api.vo.share.PagingList;
import com.qudian.idle.inspection.app.api.vo.share.SuccessRespVO;
import com.qudian.idle.inspection.app.business.handler.auth.InspectionPermissionQueryHandler;
import com.qudian.idle.inspection.app.business.service.TaskService;
import com.qudian.idle.inspection.app.business.util.PageUtils;
import com.qudian.idle.inspection.app.infrastructure.repository.database.mapper.TaskMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 任务业务服务实现
 * 说明：后续接入仓储与领域服务，完善参数校验与返回封装。
 */
@Service
public class TaskServiceImpl implements TaskService {

    @Resource
    private TaskMapper taskMapper;

    @Resource
    private InspectionPermissionQueryHandler inspectionPermissionQueryHandler;


    @Override
    public PagingList<TaskListItemRespVO> queryTaskList(TaskListQueryReqVO query) {
        InspectionPermissionQueryReqVO inspectionPermissionQueryReqVO = new InspectionPermissionQueryReqVO();
        inspectionPermissionQueryReqVO.setEmployeeId(query.getUserId());
        //查询员工岗位
        InspectionPermissionQueryRespVO respVO = inspectionPermissionQueryHandler.query(inspectionPermissionQueryReqVO);
        query.setJobPermissionList(respVO.getJobPermissionList());

        //查询任务大厅数据并分页返回

        PageInfo<TaskListItemRespVO> pageInfo = PageUtils.getPageInfo(query, () -> taskMapper.selectByTaskTypeAndJobLevel(query));

        return PageUtils.getPagingList(pageInfo);
    }


    /**
     * 认领任务 扫码
     * @param command
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SuccessRespVO claimTask(TaskClaimReqVO command) {
        if (command == null || command.getTaskNo() == null) {
            throw new IllegalArgumentException("参数错误：缺少主任务号");
        }
        if (command.getUserId() == null) {
            throw new IllegalArgumentException("参数错误：缺少用户ID");
        }

        // 查询员工岗位权限
        InspectionPermissionQueryReqVO req = new InspectionPermissionQueryReqVO();
        req.setEmployeeId(command.getUserId());
        InspectionPermissionQueryRespVO permResp = inspectionPermissionQueryHandler.query(req);
        List<InspectionPermissionQueryRespVO.JobPermission> jobPermissionList =
                permResp == null ? null : permResp.getJobPermissionList();
        if (jobPermissionList == null || jobPermissionList.isEmpty()) {
            throw new IllegalStateException("无岗位权限，无法认领任务");
        }

        // 基于 taskNo + 权限挑选一个可认领的子任务ID
        Long subId = taskMapper.selectClaimableSubIdByTaskNo(command.getTaskNo(), jobPermissionList);
        if (subId == null) {
            throw new IllegalStateException("任务不可认领或无权限认领");
        }

        // 乐观更新（status: 0 -> 1）
        LocalDateTime deadlineTime = LocalDateTime.now().plusHours(12);
        int updated = taskMapper.claimTaskSub(subId, command.getUserId(), deadlineTime);
        if (updated <= 0) {
            throw new IllegalStateException("任务已被他人认领，请刷新后重试");
        }

        return new SuccessRespVO();
    }

    /**
     * 批量认领任务
     * @param command
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskBatchClaimRespVO claimTasksByNos(TaskClaimReqVO command) {
        if (command == null || command.getUserId() == null) {
            throw new IllegalArgumentException("参数错误：缺少用户ID");
        }
        if (command.getTaskNos() == null || command.getTaskNos().isEmpty()) {
            throw new IllegalArgumentException("参数错误：缺少主任务号列表");
        }

        // 去重并保序
        List<String> taskNos = command.getTaskNos().stream()
                .filter(Objects::nonNull)
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toCollection(LinkedHashSet::new))
                .stream().collect(Collectors.toList());
        int total = taskNos.size();
        if (total == 0) {
            throw new IllegalArgumentException("参数错误：主任务号列表为空");
        }

        // 查询员工岗位权限
        InspectionPermissionQueryReqVO req = new InspectionPermissionQueryReqVO();
        req.setEmployeeId(command.getUserId());
        InspectionPermissionQueryRespVO permResp = inspectionPermissionQueryHandler.query(req);
        List<InspectionPermissionQueryRespVO.JobPermission> jobPermissionList =
                permResp == null ? null : permResp.getJobPermissionList();

        if (jobPermissionList == null || jobPermissionList.isEmpty()) {
            return TaskBatchClaimRespVO.builder()
                    .total(total)
                    .successCount(0)
                    .failureCount(total)
                    .build();
        }

        int success = 0;
        for (String taskNo : taskNos) {
            try {
                // 为当前 taskNo 选择一条可认领子任务
                Long subId = taskMapper.selectClaimableSubIdByTaskNo(taskNo, jobPermissionList);
                if (subId == null) {
                    continue; // 不可认领或无权限 -> 失败计数由差额体现
                }
                LocalDateTime deadlineTime = LocalDateTime.now().plusHours(12);
                int updated = taskMapper.claimTaskSub(subId, command.getUserId(), deadlineTime);
                if (updated > 0) {
                    success++;
                }
            } catch (Exception ignore) {
                // 单条失败不回滚整批，继续下一条
            }
        }

        return TaskBatchClaimRespVO.builder()
                .total(total)
                .successCount(success)
                .failureCount(total - success)
                .build();
    }


}