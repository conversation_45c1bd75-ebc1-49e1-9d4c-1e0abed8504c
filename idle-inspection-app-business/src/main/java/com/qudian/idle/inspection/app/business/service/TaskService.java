package com.qudian.idle.inspection.app.business.service;

import com.qudian.idle.inspection.app.api.vo.request.task.*;
import com.qudian.idle.inspection.app.api.vo.response.task.*;
import com.qudian.idle.inspection.app.api.vo.share.PagingList;
import com.qudian.idle.inspection.app.api.vo.share.SuccessRespVO;

/**
 * 任务业务服务接口
 * 说明：承接 Facade 入参与出参，屏蔽控制层与领域实现细节。
 */
public interface TaskService {

    PagingList<TaskListItemRespVO> queryTaskList(TaskListQueryReqVO query);

    SuccessRespVO claimTask(TaskClaimReqVO command);

    TaskBatchClaimRespVO claimTasksByNos(TaskClaimReqVO command);
}