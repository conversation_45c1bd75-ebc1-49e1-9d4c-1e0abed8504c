package com.qudian.idle.inspection.app.business.helper;

import com.qudian.idle.inspection.app.api.enums.auth.AuthTypeEnum;
import com.qudian.idle.inspection.app.api.enums.auth.SendMsgTypeEnum;
import com.qudian.idle.inspection.app.api.vo.request.auth.UserTokenReqVO;
import com.qudian.idle.inspection.app.common.enums.CacheKeyEnum;
import com.qudian.idle.inspection.app.common.utils.common.KaptchaUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class AuthHelper {

    @Resource
    private KaptchaUtil kaptchaUtil;

    @Resource
    private JwtUtil jwtUtil;

    public Pair<Boolean,String> checkMobileVerificationCode(String mobile, String code, SendMsgTypeEnum sendMsgTypeEnum, String platform,Integer sourceSystem) {
        log.info("checkMobileVerificationCode mobile:{},code:{}", mobile, code);
        String key = buildSmsKey(mobile, platform, sendMsgTypeEnum, sourceSystem);
        log.info("checkMobileVerificationCode redis key {}", key);
        boolean verified = kaptchaUtil.canBeVerified(key, code);
        if (!verified) {
            log.info("校验失败!验证码code:{}错误!",code);
            return Pair.of(false, null);
        }
        String token = jwtUtil.getEmailOrMobileToken(AuthTypeEnum.MOBILE, sendMsgTypeEnum, mobile);
        return Pair.of(true, token);
    }

    public String buildSmsKey(String mobile, String platform, SendMsgTypeEnum sendMsgTypeEnum, Integer sourceSystem) {
        return String.format(CacheKeyEnum.SMS_REDIS_PREFIX.getPrefix(), sendMsgTypeEnum.getCode(), mobile, platform,sourceSystem);

    }

    public String getToken(UserTokenReqVO userTokenReqVO) {
        return jwtUtil.getToken(userTokenReqVO);
    }
}
