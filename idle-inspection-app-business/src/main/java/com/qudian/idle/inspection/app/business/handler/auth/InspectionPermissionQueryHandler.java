package com.qudian.idle.inspection.app.business.handler.auth;

import com.qudian.idle.inspection.app.api.vo.request.auth.InspectionPermissionQueryReqVO;
import com.qudian.idle.inspection.app.api.vo.response.auth.InspectionPermissionQueryRespVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class InspectionPermissionQueryHandler {



    public InspectionPermissionQueryRespVO query(InspectionPermissionQueryReqVO query) {
        return null;
    }
}
