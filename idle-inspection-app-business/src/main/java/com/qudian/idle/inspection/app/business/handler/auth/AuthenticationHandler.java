package com.qudian.idle.inspection.app.business.handler.auth;

import cn.hutool.core.lang.Assert;
import com.qudian.idle.inspection.app.api.vo.request.auth.AuthenticationReqVO;
import com.qudian.idle.inspection.app.api.vo.request.auth.EmployeeInfoQueryReqVO;
import com.qudian.idle.inspection.app.api.vo.response.auth.AuthenticationRespVO;
import com.qudian.idle.inspection.app.api.vo.response.auth.UserAuthDTO;
import com.qudian.idle.inspection.app.business.helper.JwtUtil;
import com.qudian.idle.inspection.app.infrastructure.repository.EmployeeRepository;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.EmployeePO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.Serializable;

@Component
@Slf4j
public class AuthenticationHandler  {

    @Resource
    private JwtUtil jwtUtil;

    @Resource
    private EmployeeRepository employeeRepository;

    public AuthenticationRespVO handle(AuthenticationReqVO command) {
        UserAuthDTO userAuthDTO = verifyToken(command.getToken());
        EmployeePO employee = employeeRepository.getById(userAuthDTO.getUserId());
        Assert.notNull(employee);
        return AuthenticationRespVO.builder()
                .userId(employee.getId())
                .sourceSystem(employee.getSourceSystem())
                .build();
    }

    private UserAuthDTO verifyToken(String token) {
        return jwtUtil.verifyToken(token);
    }
}
