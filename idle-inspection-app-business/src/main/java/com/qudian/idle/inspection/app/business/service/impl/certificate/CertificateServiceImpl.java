package com.qudian.idle.inspection.app.business.service.impl.certificate;

import com.qudian.idle.inspection.app.api.vo.request.certificate.CertificateBaseShowReqVO;
import com.qudian.idle.inspection.app.api.vo.response.certificate.CertificateBaseShowRespVO;
import com.qudian.idle.inspection.app.business.service.CertificateService;
import com.qudian.idle.inspection.app.business.service.command.CertificateGeneratesCmd;
import com.qudian.idle.inspection.app.business.util.CertificateGenerator;
import com.qudian.idle.inspection.app.infrastructure.assembler.certificate.CertificateBaseStruct;
import com.qudian.idle.inspection.app.infrastructure.repository.CertificateRepository;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.certificate.CertificatePO;
import com.qudian.idle.inspection.app.infrastructure.repository.rpc.remote.ToolRemote;
import com.qudian.pdt.toolkit.common.exception.BizException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ListIterator;
import java.util.Objects;

/**
 * <p>文件名称:com.qudian.idle.inspection.app.business.service.impl.certificate.CertificateServiceImpl</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/21
 */
@Service
@Slf4j
public class CertificateServiceImpl implements CertificateService {
    @Resource
    private CertificateRepository certificateRepository;
    @Resource
    private ToolRemote toolRemote;

    @Resource
    private CertificateBaseStruct certificateBaseStruct;

    @Override
    public void generate(CertificateGeneratesCmd generatesCmd) {
        // 生成证书编号14位
        String certificateNo = CertificateGenerator.generateCertificateNo();

        // 生成防伪扣编号12位
        String antiCounterfeitNo = CertificateGenerator.generateAntiCounterfeitNo();

        // 创建证书记录
        CertificatePO certificatePO = new CertificatePO()
                .setCertificateNo(certificateNo)
                .setAntiCounterfeitNo(antiCounterfeitNo)
                .setVerdict(generatesCmd.getVerdict())
                .setSn(generatesCmd.getSn())
                .setOrderNo(generatesCmd.getOrderNo())
                .setSkuId(generatesCmd.getSkuId());

        // TODO: 查询商品信息并设置商品快照
        // TODO: 查询订单信息并设置订单快照

        // 保存证书记录
        certificateRepository.save(certificatePO);

        log.info("Certificate generated successfully. CertificateNo: {}, AntiCounterfeitNo: {}, SN: {}",
                certificateNo, antiCounterfeitNo, generatesCmd.getSn());
    }

    @Override
    public CertificateBaseShowRespVO query(CertificateBaseShowReqVO reqVO) {
        CertificatePO certificatePO = certificateRepository.selectBySn(reqVO.getSn()).orElseThrow(() -> new BizException("Certificate not found by sn:" + reqVO.getSn()));
        this.increaseQueryCnt(certificatePO.getId(), certificatePO.getQueryCnt());
        CertificateBaseShowRespVO showRespVO = certificateBaseStruct.Po2ShowVo(certificatePO);
        //加签商品主图,覆盖原先string值
        if (!Objects.isNull(showRespVO.getGoodsInfo()) && !CollectionUtils.isEmpty(showRespVO.getGoodsInfo().getMainImages())) {
            ListIterator<String> it = showRespVO.getGoodsInfo().getMainImages().listIterator();
            while (it.hasNext()) {
                //TODO@ch 加签
                it.set(it.next());
            }
        }
        return showRespVO;
    }

    /**
     * 累加查询次数
     *
     * @param id              id
     * @param currentQueryCnt 当前查询CNT
     */
    private void increaseQueryCnt(Long id, Integer currentQueryCnt) {
        certificateRepository.lambdaUpdate().eq(CertificatePO::getId, id)
                .set(CertificatePO::getQueryCnt, ++currentQueryCnt)
                .update();
    }

}
