package com.qudian.idle.inspection.app.business.helper;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.qudian.idle.inspection.app.api.enums.auth.AuthTypeEnum;
import com.qudian.idle.inspection.app.api.enums.auth.PlatformEnum;
import com.qudian.idle.inspection.app.api.enums.auth.SendMsgTypeEnum;
import com.qudian.idle.inspection.app.api.vo.request.auth.UserTokenReqVO;
import com.qudian.idle.inspection.app.api.vo.response.auth.UserAuthDTO;
import com.qudian.idle.inspection.app.common.utils.json.JsonUtil;
import com.qudian.idle.inspection.app.common.utils.middleware.RedisUtil;
import com.qudian.pdt.toolkit.common.enums.ExceptionEnum;
import com.qudian.pdt.toolkit.common.exception.BizException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;

/**
 * @author: zhongshouzhi
 * @description: JWT封装工具类
 * @date: 2020/9/12 上午10:16
 */
@Slf4j
@Component
public class JwtUtil {
    /* 有效期(月) */
    private static final int EXPIRE_MONTH = 6;
    /* 平台 key */
    private static final String PLATFORM = "platform";
    /* 用户id key */
    private static final String USER_ID = "userId";
    private static final String EMAIL = "email";

    private static final String MOBILE = "mobile";
    /* 是否新用户key */
    private static final String IS_NEW_USER = "isNewUser";
    /* secret key */
    private static final String SECRET_KEY_PREFIX = "AUTH_TOKEN_SECRET_";
    private static final String EMAIL_REGISTER_KEY_PREFIX = "EMAIL_TOKEN_SECRET_";

    @Resource
    private RedisUtil redisUtil;

    /**
     * 生成token通用方法
     *
     * @param userAuthRequestVO 加密参数
     * @return token
     */
    public String getToken(UserTokenReqVO userAuthRequestVO) {
        /* 校验参数 */
        if (ObjectUtils.isEmpty(userAuthRequestVO)) {
            throw new BizException(ExceptionEnum.JWT_PARAM_VERIFY_ERROR);
        }
        /* 默认APP平台 */
        if(StringUtils.isEmpty(userAuthRequestVO.getPlatform())) {
            userAuthRequestVO.setPlatform(PlatformEnum.APP.getCode());
        }
        /* 必备参数校验:平台、用户id */
        if (StringUtils.isEmpty(userAuthRequestVO.getPlatform())
                || ObjectUtils.isEmpty(userAuthRequestVO.getUserId())) {
            throw new BizException(ExceptionEnum.JWT_PARAM_VERIFY_ERROR);
        }
        /* 识别平台 */
        String platform = userAuthRequestVO.getPlatform();
        PlatformEnum platformEnum = PlatformEnum.getByCode(platform);
        if (null == platformEnum) {
            throw new BizException(ExceptionEnum.JWT_UNKNOWN_PLATFORM);
        }
        //获取秘钥
        String secretKey = getSecretKey(userAuthRequestVO);
        String secret = userAuthRequestVO.getUserId() + ".secret." + RandomUtil.randomNumbers(4);
        redisUtil.setex(secretKey, secret, getExpireSeconds());

        //设置jwt加密方式
        Algorithm algorithm = Algorithm.HMAC256(secret);
        //设置头信息
        HashMap<String, Object> header = new HashMap<>(2);
        header.put("typ", "JWT");
        header.put("alg", "HS256");
        Date expiresAt = getExpireDate();
        //生成jwt
        String token = JWT.create()
                .withClaim(USER_ID, userAuthRequestVO.getUserId())
                .withClaim(PLATFORM, userAuthRequestVO.getPlatform())
                .withHeader(header)
                .withExpiresAt(expiresAt)
                .sign(algorithm);
        log.info("ID:{} IDLE-AUTH-TOKEN：{}，过期时间：{}", userAuthRequestVO.getUserId(), token, DateUtil.format(expiresAt, "yyyy-MM-dd HH:mm:ss"));
        return token;
    }

    public String getEmailOrMobileToken(AuthTypeEnum authTypeEnum, SendMsgTypeEnum msgTypeEnum, String emailOrMobile) {
        String secretKey = msgTypeEnum.getProcessTokenPrefix() + emailOrMobile;
        String secret = emailOrMobile + ".secret." + RandomUtil.randomNumbers(4);
        // 15min
        redisUtil.setex(secretKey, secret, 15 * 60);
        //设置jwt加密方式
        Algorithm algorithm = Algorithm.HMAC256(secret);
        //设置头信息
        HashMap<String, Object> header = new HashMap<>(2);
        header.put("typ", "JWT");
        header.put("alg", "HS256");
        Date expiresAt = getEmailRegisterExpireDate();
        //生成jwt
        String token = JWT.create()
                .withClaim(authTypeEnum.getCode(), emailOrMobile)
                .withHeader(header)
                .withExpiresAt(expiresAt)
                .sign(algorithm);
        log.info("IDLE-EMAIL_OR_MOBILE-AUTH-TOKEN：{}，过期时间：{}", token, DateUtil.format(expiresAt, "yyyy-MM-dd HH:mm:ss"));
        return token;
    }

    public String verifyEmailOrMobileToken(AuthTypeEnum authTypeEnum, SendMsgTypeEnum msgTypeEnum, String token) {
        String emailOrMobile;
        try {
            DecodedJWT jwt = JWT.decode(token);
            /* 解析信息 */
            emailOrMobile = jwt.getClaim(authTypeEnum.getCode()).asString();
            String secretKey = msgTypeEnum.getProcessTokenPrefix() + emailOrMobile;
            String secret = redisUtil.get(secretKey, 0);
            /* 鉴权 */
            Algorithm algorithm = Algorithm.HMAC256(secret);
            JWTVerifier verifier = JWT.require(algorithm).build();
            verifier.verify(token);
        } catch (Exception e) {
            log.warn("verifyToken error {}", e.getMessage(),e);
            throw new BizException(ExceptionEnum.JWT_VERIFY_FAIL);
        }
        return emailOrMobile;
    }

    /**
     * 生成secretkey，规则：SECRET_KEY_PREFIX_{userId}_{platform}
     *
     * @param userAuthRequestVO
     * @return
     */
    private String getSecretKey(UserTokenReqVO userAuthRequestVO) {
        log.info("userAuthRequestVO:{}", JsonUtil.toJsonString(userAuthRequestVO));
        return SECRET_KEY_PREFIX + userAuthRequestVO.getUserId() + "_" + userAuthRequestVO.getPlatform();
    }

    private Date getExpireDate() {
        Calendar systemTime = Calendar.getInstance();
        systemTime.add(Calendar.MONTH, EXPIRE_MONTH);
        Date expiresAt = systemTime.getTime();
        return expiresAt;
    }

    private Date getEmailRegisterExpireDate() {
        Calendar systemTime = Calendar.getInstance();
        systemTime.add(Calendar.MINUTE, 15);
        Date expiresAt = systemTime.getTime();
        return expiresAt;
    }

    private int getExpireSeconds() {
        int expireSeconds = EXPIRE_MONTH * 30 * 24 * 60 * 60;
        return expireSeconds;
    }

    /**
     * 鉴权，换取用户信息
     *
     * @param token
     * @return userId
     */
    public UserAuthDTO verifyToken(String token) {
        UserAuthDTO userAuthResponseVO = new UserAuthDTO();
        DecodedJWT jwt;
        try {
            jwt = JWT.decode(token);
            /* 解析信息 */
            Long userId = jwt.getClaim(USER_ID).asLong();
            String platform = jwt.getClaim(PLATFORM).asString();

            String secretKey = getSecretKey(UserTokenReqVO.builder()
                    .userId(userId)
                    .platform(platform)
                    .build());

            String secret = redisUtil.get(secretKey, 0);

            /* 鉴权 */
            Algorithm algorithm = Algorithm.HMAC256(secret);
            JWTVerifier verifier = JWT.require(algorithm).build();
            verifier.verify(token);
            /* 解析参数 */
            userAuthResponseVO.setUserId(String.valueOf(userId));
            userAuthResponseVO.setPlatform(platform);
        } catch (Exception e) {
            log.warn("verifyToken error {}", e.getMessage(),e);
            throw new BizException(ExceptionEnum.JWT_VERIFY_FAIL);
        }
        return userAuthResponseVO;
    }

    /**
     * 清理token，下线用户 app端
     *
     * @param userId 用户id
     */
    public void delToken(Long userId) {
        try {
            redisUtil.del(getSecretKey(UserTokenReqVO.builder().userId(userId).platform(PlatformEnum.APP.getCode()).build()));
        } catch (Exception e) {
            log.warn("app token清除失败", e);
            throw e;
        }
    }

    /**
     * pad端下线
     *
     * @param userId 用户id
     */
    public void delPadToken(String userId) {
        try {
            redisUtil.del(getSecretKey(UserTokenReqVO.builder().userId(Long.valueOf(userId)).platform(PlatformEnum.PAD_CODE.getCode()).build()));
        } catch (Exception e) {
            log.warn("PAD token清除失败", e);
            throw e;
        }
    }

    /**
     * 校验token是否存在, true存在 false不存在
     * @param userAuthRequestVO
     * @return
     */
    public boolean checkTokenExists(UserTokenReqVO userAuthRequestVO) {
        /* 校验参数 */
        if (ObjectUtils.isEmpty(userAuthRequestVO)) {
            return false;
        }
        //获取秘钥
        String secretKey = getSecretKey(UserTokenReqVO.builder().userId(userAuthRequestVO.getUserId()).platform(PlatformEnum.APP.getCode()).build());
        String token = redisUtil.get(secretKey, 0);
        return StringUtils.isNotBlank(token);
    }
}
