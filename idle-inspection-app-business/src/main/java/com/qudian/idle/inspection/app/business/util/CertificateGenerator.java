package com.qudian.idle.inspection.app.business.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <p>文件名称:com.qudian.idle.inspection.app.business.util.CertificateGenerator</p>
 * <p>文件描述: 奢侈品鉴别证书编号生成器</p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: 生成14位全局唯一的鉴定证书编号，包含业务含义且不易被破解猜测</p>
 * <p>其他说明:
 *   编号格式：XXYYMMDDHHMM + RRR + C (14位)
 *   XX: 业务类型标识 (01-奢侈品鉴别)
 *   YYMMDDHHMM: 时间戳 (年月日时分，8位)
 *   RRR: 随机数 (3位)
 *   C: 校验位 (1位，基于前13位计算)
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/22
 */
@Slf4j
public class CertificateGenerator {

    /**
     * 奢侈品鉴别业务类型标识
     */
    private static final String LUXURY_INSPECTION_PREFIX = "01";

    /**
     * 生成14位全局唯一的鉴定证书编号
     *
     * @return 14位数字证书编号
     */
    public static String generateCertificateNo() {
        // 1. 业务类型标识 (2位)
        String businessType = LUXURY_INSPECTION_PREFIX;

        // 2. 时间戳 (8位): YYMMDD + MM (年月日+时间标识)
        LocalDateTime now = LocalDateTime.now();
        String datePart = now.format(DateTimeFormatter.ofPattern("yyMMdd")); // 6位
        int totalMinutes = now.getHour() * 60 + now.getMinute(); // 当天的总分钟数
        String timePart = String.format("%02d", totalMinutes % 100); // 取后两位作为时间标识
        String timestamp = datePart + timePart;

        // 3. 随机数 (3位): 使用ThreadLocalRandom提高并发性能
        String randomPart = String.format("%03d", ThreadLocalRandom.current().nextInt(1000));

        // 4. 前13位 = 2位业务类型 + 8位时间戳 + 3位随机数
        String prefix13 = businessType + timestamp + randomPart;

        // 5. 计算校验位 (1位): 使用简化的校验算法
        String checkDigit = calculateCheckDigit(prefix13);

        // 6. 组合最终的14位证书编号
        String certificateNo = prefix13 + checkDigit;

        log.info("Generated certificate number: {}, businessType: {}, timestamp: {}, random: {}, checkDigit: {}",
                certificateNo, businessType, timestamp, randomPart, checkDigit);

        return certificateNo;
    }

    /**
     * 生成防伪扣编号 (12位)
     * 格式：YYMMDD + HHMMSS (年月日时分秒)
     *
     * @return 12位数字防伪扣编号
     */
    public static String generateAntiCounterfeitNo() {
        LocalDateTime now = LocalDateTime.now();
        String antiCounterfeitNo = now.format(DateTimeFormatter.ofPattern("yyMMddHHmmss"));

        log.info("Generated anti-counterfeit number: {}", antiCounterfeitNo);

        return antiCounterfeitNo;
    }

    /**
     * 计算校验位
     * 使用简化的校验算法
     *
     * @param input 前13位数字字符串
     * @return 校验位 (0-9)
     */
    private static String calculateCheckDigit(String input) {
        int sum = 0;

        // 计算所有数字的加权和
        for (int i = 0; i < input.length(); i++) {
            int digit = Character.getNumericValue(input.charAt(i));
            // 使用位置权重
            sum += digit * (i % 3 + 1);
        }

        // 计算校验位
        int checkDigit = sum % 10;
        return String.valueOf(checkDigit);
    }

    /**
     * 验证证书编号格式是否正确
     *
     * @param certificateNo 证书编号
     * @return true-格式正确，false-格式错误
     */
    public static boolean validateCertificateNo(String certificateNo) {
        if (certificateNo == null || certificateNo.length() != 14) {
            return false;
        }

        // 检查是否全为数字
        if (!certificateNo.matches("\\d{14}")) {
            return false;
        }

        // 检查业务类型标识
        if (!certificateNo.startsWith(LUXURY_INSPECTION_PREFIX)) {
            return false;
        }

        // 验证校验位
        String prefix13 = certificateNo.substring(0, 13);
        String expectedCheckDigit = calculateCheckDigit(prefix13);
        String actualCheckDigit = certificateNo.substring(13);

        return expectedCheckDigit.equals(actualCheckDigit);
    }

    /**
     * 解析证书编号中的时间信息
     *
     * @param certificateNo 证书编号
     * @return 时间字符串 (格式: yyyy-MM-dd)
     */
    public static String parseTimestamp(String certificateNo) {
        if (!validateCertificateNo(certificateNo)) {
            throw new IllegalArgumentException("Invalid certificate number format");
        }

        String datePart = certificateNo.substring(2, 8); // YYMMDD (6位)
        String timePart = certificateNo.substring(8, 10); // MM (2位时间标识)

        try {
            LocalDateTime dateTime = LocalDateTime.parse("20" + datePart + "0000",
                    DateTimeFormatter.ofPattern("yyyyMMddHHmm"));
            return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) +
                   " (时间标识: " + timePart + ")";
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid timestamp in certificate number", e);
        }
    }
}
