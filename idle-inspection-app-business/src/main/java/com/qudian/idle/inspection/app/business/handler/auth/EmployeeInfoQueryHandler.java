package com.qudian.idle.inspection.app.business.handler.auth;

import com.qudian.idle.inspection.app.api.vo.request.auth.EmployeeInfoQueryReqVO;
import com.qudian.idle.inspection.app.api.vo.response.auth.EmployeeInfoQueryRespVO;
import com.qudian.idle.inspection.app.infrastructure.repository.EmployeeRepository;
import com.qudian.idle.inspection.app.infrastructure.repository.database.po.EmployeePO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class EmployeeInfoQueryHandler {

    @Resource
    private EmployeeRepository employeeRepository;

    public EmployeeInfoQueryRespVO handle(EmployeeInfoQueryReqVO query) {
        EmployeePO employee = employeeRepository.getById(query.getUserId());
        return EmployeeInfoQueryRespVO.builder()
                .userId(employee.getId())
                .mobile(employee.getMobile())
                .name(employee.getName())
                .sourceSystem(employee.getSourceSystem())
                .build();
    }
}
