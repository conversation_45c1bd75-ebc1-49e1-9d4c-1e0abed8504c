package com.qudian.idle.inspection.app.api.vo.response.task;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 任务列表条目响应
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class TaskListItemRespVO implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 商品名
     */
    private String productName;


    /**
     * 任务类型
     * 1：真假鉴定；2：瑕疵鉴定；3：成色鉴定；4：拍照
     */
    private Integer taskCategoryId;

    /**
     * 销售阶段
     * 0：售前；1：售后（对应字段：sales_stage）
     */
    private Integer salesStage;


    /**
     * 任务状态
     * 0：未开始；1：待取货；2：进行中；3：完成；4：其他
     */
    private Integer status;


    /**
     * 任务所需人数
     */
    private Integer requiredPeople;

    /**
     * 已认领人数
     */
    private Integer claimedPeople;

    /**
     * 截止时间
     */
    private LocalDateTime deadlineTime;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

}