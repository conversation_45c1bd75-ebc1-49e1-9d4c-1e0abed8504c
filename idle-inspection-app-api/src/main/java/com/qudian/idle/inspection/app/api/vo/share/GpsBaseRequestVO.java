package com.qudian.idle.inspection.app.api.vo.share;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>文件名称:com.qudian.lme.driver.api.vo.BaseRequestVO</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2022/10/26
 */
@Data
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class GpsBaseRequestVO extends BaseRequestVO {

    private static final long serialVersionUID = -7901008933541715718L;

    private String longitude;
    private String latitude;

    public GpsBaseRequestVO(Long userId, String longitude, String latitude) {
        super(userId);
        this.longitude = longitude;
        this.latitude = latitude;
    }
}
