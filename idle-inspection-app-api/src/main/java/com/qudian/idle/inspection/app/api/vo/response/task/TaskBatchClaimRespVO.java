// java
package com.qudian.idle.inspection.app.api.vo.response.task;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 批量认领结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskBatchClaimRespVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private int total;
    private int successCount;
    private int failureCount;
}