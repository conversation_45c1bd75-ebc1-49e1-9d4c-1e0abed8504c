package com.qudian.idle.inspection.app.api.enums;

import java.util.Objects;

public enum DeleteFlagEnum {

    NORMAL(0, "正常"),
    DELETED(1, "删除");

    private Integer code;
    private String desc;

    DeleteFlagEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static DeleteFlagEnum get(Integer code) {
        for (DeleteFlagEnum deleteFlagEnum : DeleteFlagEnum.values()) {
            if (deleteFlagEnum.getCode().equals(code)) {
                return deleteFlagEnum;
            }
        }
        return null;
    }

    public boolean test(Integer code) {
        return Objects.equals(this.code, code);
    }
}
