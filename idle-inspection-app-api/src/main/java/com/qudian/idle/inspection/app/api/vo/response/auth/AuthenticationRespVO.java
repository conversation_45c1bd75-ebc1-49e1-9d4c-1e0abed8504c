package com.qudian.idle.inspection.app.api.vo.response.auth;

import com.qudian.pdt.api.toolkit.vo.response.BaseResponseVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AuthenticationRespVO extends BaseResponseVO {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 来源系统 1:检测端，2:仓库端
     *
     * @see com.qudian.idle.inspection.app.api.enums.employee.EmployeeSourceSystemEnum
     */
    private Integer sourceSystem;
}
