package com.qudian.idle.inspection.app.api.enums.auth;
import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum SendMsgTypeEnum {

    LOGIN("login","登陆","LOGIN_TOKEN_SECRET_");

    /**
     * 编码
     */
    private String code;

    /**
     * 描述
     */
    private String desc;

    /**
     *
     */
    private String processTokenPrefix;
    
    /**
     * 根据 code 判断是否是该枚举
     * @param code code
     * @return true:是，false:否
     */
    public boolean same(String code) {
        return Objects.equals(this.code, code);
    }

    /**
     * 根据 code 获取对应枚举值
     * @param code code
     * @return 对应枚举，匹配不到返回null
     */
    public static SendMsgTypeEnum getByCode(String code) {
        for (SendMsgTypeEnum value : values()) {
            if (value.same(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据 code 获取对应枚举值
     * @param code code
     * @param defaultEnum 默认值
     * @return 对应枚举，匹配不到返回 defaultEnum
     */
    public static SendMsgTypeEnum getByCode(String code,SendMsgTypeEnum defaultEnum) {
        SendMsgTypeEnum result = getByCode(code);
        return result != null ? result : defaultEnum;
    }


}
