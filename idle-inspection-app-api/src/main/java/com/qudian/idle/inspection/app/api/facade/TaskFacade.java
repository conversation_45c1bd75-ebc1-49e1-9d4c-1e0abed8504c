package com.qudian.idle.inspection.app.api.facade;

import com.qudian.idle.inspection.app.api.vo.request.task.*;
import com.qudian.idle.inspection.app.api.vo.response.task.*;
import com.qudian.idle.inspection.app.api.vo.share.PagingList;
import com.qudian.idle.inspection.app.api.vo.share.SuccessRespVO;
import com.qudian.lme.common.dto.BaseResponseDTO;

/**
 * description:
 * author: wangyixin
 * created: 2025/8/14
 */
public interface TaskFacade {

    /**
     * 创建任务
     */
    // BaseResponseDTO<SuccessRespVO> createTask(TaskCreateReqVO command); // 该方法未在原代码中定义，需根据实际需求添加

    /**
     * 任务大厅
     * @param query
     * @return
     */
     BaseResponseDTO<PagingList<TaskListItemRespVO>> queryTaskList(TaskListQueryReqVO query);

    /**
     * 认领任务接口
     * @param command 认领参数
     * @return 认领结果
     */
    BaseResponseDTO<SuccessRespVO> claimTask(TaskClaimReqVO command);

    /**
     * 我的认领任务列表
     * @param query 查询参数（需包含执行人ID，可选条件：时间区间、品牌、分类、任务类型、状态等）
     * @return 分页任务列表
     */
    BaseResponseDTO<PagingList<ClaimedTaskListItemRespVO>> queryClaimedTaskList(TaskListQueryReqVO query);


    /**
     * 查询商品的子任务列表(扫码任务)
     * @param query
     * @return
     */
     BaseResponseDTO<TaskSubListRespVO> queryTaskSubList(TaskSubListQueryReqVO query);

    /**
     * 查询子任务详情
     * @param query
     * @return
     */
    BaseResponseDTO<TaskSubDetailRespVO> queryTaskSubDetail(TaskSubListQueryReqVO query);

    /**
     * 开始子任务
     */
    BaseResponseDTO<SuccessRespVO> startTaskSub(TaskSubStartReqVO command); // 该方法未在原代码中定义，需根据实际需求添加

    /**
     * 提交子任务
     */
     BaseResponseDTO<SuccessRespVO> submitTaskSub(TaskSubSubmitReqVO command); // 该方法未在原代码中定义，需根据实际需求添加

    /**
     * 挂起和恢复
     */
     BaseResponseDTO<SuccessRespVO> changeStatus(TaskSubChangeStatusReqVO command); // 该方法未在原代码中定义，需根据实际需求添加


    /**
     * 查询用户进行中的任务数
     * @param query 查询参数
     * @return 进行中任务数
     */
    BaseResponseDTO<TaskCountRespVO> queryDoingCount(TaskCountReqVO query);








}
