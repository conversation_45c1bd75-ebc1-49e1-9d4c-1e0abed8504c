package com.qudian.idle.inspection.app.api.vo.response.certificate;

import com.qudian.pdt.api.toolkit.vo.response.BaseResponseVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>文件名称:com.qudian.idle.inspection.app.api.vo.response.certificate.CertificateBaseShowRespVO</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/21
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class CertificateBaseShowRespVO extends BaseResponseVO {
    /**
     * 证书信息
     */
    private CertificateInnerInfoRespVO certificateInfo;
    /**
     * 商品信息
     */
    private CertificateInnerGoodsSnapshotRespVO goodsInfo;
    /**
     * 订单信息
     */
    private CertificateInnerOrderSnapshotRespVO orderInfo;

    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CertificateInnerInfoRespVO implements Serializable {
        private Long certificateId; //证书id
        private String certificateNo;  //鉴别证书编号
        private String antiCounterfeitNo;  //防伪扣编号
        private Integer verdict;  //鉴定结果 1-未通过 2-通过
        private Integer queryCnt;  //报告查询次数
        private String lastQueryTime;  //上次查询时间
        private String currentQueryTime;  //本次查询时间
    }

    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CertificateInnerGoodsSnapshotRespVO implements Serializable {
        private String goodsName;  //商品名称
        private String brand;  //品牌
        private List<String> mainImages; //主图
    }

    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CertificateInnerOrderSnapshotRespVO implements Serializable {
        private String amount;  //订单金额
        private String orderTime;  //下单时间
        private String orderSource;  //订单来源
        private String purchaserName;  //购买人昵称
    }
}
