package com.qudian.idle.inspection.app.api.vo.response.task;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * SKU 关联商品配置（sku_relation_step）
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class SkuRelationStepVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 描述字段开关
     */
    private Integer descSwitch;

    /**
     * 描述名称
     */
    private String descName;

    /**
     * 描述提示
     */
    private String descTips;

    /**
     * 描述的描述
     */
    private String descDesc;

    /**
     * 描述值类型：1图文，2文
     */
    private Integer descValueType;

    /**
     * 图文设置\-拍摄照片：1是，0否
     */
    private Integer descGtsPhoto;

    /**
     * 图文设置\-必填：1是，0否
     */
    private Integer descGtsRequired;

    /**
     * 图文设置\-对比图：1是，0否
     */
    private Integer descGtsCompChart;

    /**
     * 图文设置\-照片数量 \> 多少张
     */
    private Integer descGtsPhotoNum;

    /**
     * 图文设置\-文字描述 \< 多少字
     */
    private Integer descGtsTextNum;

    /**
     * 用户选择的sku
     */
    private String descInputSkuId;

    /**
     * 用户输入的描述内容
     */
    private String descInputContent;

    /**
     * 用户输入的照片路径
     */
    private List<String> descInputPhotos;

    /**
     * 用户输入的比对图照片路径
     */
    private List<String> descInputCompPhotos;
}