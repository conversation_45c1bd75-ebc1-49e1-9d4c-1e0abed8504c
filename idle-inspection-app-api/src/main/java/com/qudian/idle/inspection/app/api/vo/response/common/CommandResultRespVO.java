package com.qudian.idle.inspection.app.api.vo.response.common;

import com.qudian.pdt.api.toolkit.vo.response.BaseResponseVO;
import lombok.Data;

import java.io.Serializable;

@Data
public class CommandResultRespVO<T> extends BaseResponseVO {

    /**
     * 操作id
     */
    private T operateId;

    /**
     * 成功标识
     */
    private Boolean success;

    /**
     * 错误信息
     */
    private String failMessage;


    public static CommandResultRespVO buildError(String failMessage) {
        CommandResultRespVO result = new CommandResultRespVO();
        result.setSuccess(false);
        result.setFailMessage(failMessage);
        return result;
    }


    public static<T> CommandResultRespVO<T> buildSuccess(T operateId) {
        CommandResultRespVO result = new CommandResultRespVO();
        result.setSuccess(true);
        result.setOperateId(operateId);
        return result;
    }
}
