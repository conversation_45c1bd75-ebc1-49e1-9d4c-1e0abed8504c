package com.qudian.idle.inspection.app.api.vo.request.auth;

import lombok.Data;

import java.io.Serializable;

@Data
public class LoginBySmsCodeReqVO implements Serializable {

    /**
     * 手机号或者邮箱
     */
    private String emailOrMobile;
    /**
     * 使用手机验证的code还是邮箱， 值是email和mobile两种
     */
    private String authType;
    /**
     * 验证码
     */
    private String code;
    /**
     * 发送验证码是什么场景发送的 regOrLogin(注册登录)、delUser(注销)、changeMobile(更换手机号)
     */
    private String sendType;

    /**
     * 平台
     */
    private String platform;

    /**
     * 来源系统
     */
    private Integer sourceSystem;

}
