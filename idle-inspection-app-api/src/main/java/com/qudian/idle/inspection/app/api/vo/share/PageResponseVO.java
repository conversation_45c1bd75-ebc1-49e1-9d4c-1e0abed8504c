package com.qudian.idle.inspection.app.api.vo.share;

import lombok.Data;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2024</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> board)
 * @version 1.0
 */
@Data
@ToString(callSuper = true)
public class PageResponseVO extends BaseResponseVO {
    private static final long serialVersionUID = 7090170000356047884L;
    // 当前页码
    private Integer page;
    // 每页显示的记录数
    private Integer rowNum;
    // 总记录数
    private Integer records;
    // 返回数据
    private List<Object> rows = new ArrayList<>();

}
