package com.qudian.idle.inspection.app.api.vo.share;

import com.qudian.pdt.api.toolkit.vo.response.BaseResponseVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SuccessRespVO extends BaseResponseVO {

    private static final long serialVersionUID = -7935856248164155872L;

    Boolean success;
}
