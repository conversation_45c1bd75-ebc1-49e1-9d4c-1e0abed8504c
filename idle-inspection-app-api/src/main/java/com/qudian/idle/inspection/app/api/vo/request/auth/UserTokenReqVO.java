package com.qudian.idle.inspection.app.api.vo.request.auth;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserTokenReqVO implements Serializable {
    /**
     * 平台
     */
    private String platform;
    /**
     * 用户ID，全平台统一对应的ID
     */
    private Long userId;
}
