package com.qudian.idle.inspection.app.api.vo.request.task;

import com.qudian.pdt.api.toolkit.vo.request.BaseRequestVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * description:
 * author: wangyixin
 * created: 2025/8/15
 */

@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class TaskSubStartReqVO extends BaseRequestVO {

    /**
     * 子任务号
     */
    private String taskSubNo;
}
