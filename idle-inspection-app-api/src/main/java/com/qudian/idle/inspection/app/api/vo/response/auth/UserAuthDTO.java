
package com.qudian.idle.inspection.app.api.vo.response.auth;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserAuthDTO {

    /**
     * 平台
     */
    private String platform;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 应用apollo配置版本
     */
    private String configVersion;
    /**
     * 应用当前版本
     */
    private String applicationVersion;
    /**
     * 设备型号  ios或者是android
     */
    private String os;
}
