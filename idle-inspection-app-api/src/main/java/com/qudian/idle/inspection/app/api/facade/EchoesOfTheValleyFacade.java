package com.qudian.idle.inspection.app.api.facade;

import com.qudian.idle.inspection.app.api.vo.request.ValleyEchosReqVO;
import com.qudian.lme.common.dto.BaseResponseDTO;
import jakarta.validation.Valid;

/**
 * <p>文件描述: 测试服务连通性</p>
 * <p>版权所有: Copyright(C)2019-2024</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> architecture board)
 * @version 1.0
 */
public interface EchoesOfTheValleyFacade {
    BaseResponseDTO<ValleyEchosReqVO> echo(@Valid ValleyEchosReqVO reqVO);
}
