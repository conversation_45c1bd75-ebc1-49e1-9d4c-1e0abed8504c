package com.qudian.idle.inspection.app.api.enums.auth;

import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum PlatformEnum {

    APP("app", "app"),
    PLATFORM("platform", "平台登录"),
    APPLE("apple", "苹果三方登录"),
    FACEBOOK("facebook", "facebook三方登录"),
    GOOGLE("google", "谷歌三方登录"),
    PAD_CODE("padCode", "pad扫码登录"),;
    ;

    /**
     * 编码
     */
    private String code;

    /**
     * 描述
     */
    private String desc;

    /**
     * 根据 code 判断是否是该枚举
     *
     * @param code code
     * @return true:是，false:否
     */
    public boolean same(String code) {
        return Objects.equals(this.code, code);
    }

    /**
     * 根据 code 获取对应枚举值
     *
     * @param code code
     * @return 对应枚举，匹配不到返回null
     */
    public static PlatformEnum getByCode(String code) {
        for (PlatformEnum value : values()) {
            if (value.same(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据 code 获取对应枚举值
     *
     * @param code        code
     * @param defaultEnum 默认值
     * @return 对应枚举，匹配不到返回 defaultEnum
     */
    public static PlatformEnum getByCode(String code, PlatformEnum defaultEnum) {
        PlatformEnum result = getByCode(code);
        return result != null ? result : defaultEnum;
    }
}
