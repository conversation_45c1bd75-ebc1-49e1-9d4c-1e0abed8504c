package com.qudian.idle.inspection.app.api.vo.response.task;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * description:
 * author: wangyixin
 * created: 2025/8/14
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ClaimedTaskListItemRespVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 任务号
     */
    private String taskNo;

    /**
     * 子任务号
     */
    private String taskSubNo;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 商品名
     */
    private String productName;

    /**
     * 任务类型
     * 1：真假鉴定；2：瑕疵鉴定；3：成色鉴定；4：拍照
     */
    private Integer taskType;


    /**
     * 截止时间
     */
    private LocalDateTime deadlineTime;

    /**
     * 任务状态
     * 0：未开始；1：待取货；2：进行中；3：完成；4：其他
     */
    private Integer status;

    /**
     * 商品状态 可取货  不可取货
     */
    private Integer productStatus;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;

}
