package com.qudian.idle.inspection.app.api.vo.request.task;

import com.qudian.pdt.api.toolkit.vo.request.BaseRequestVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * description:
 * author: wangyixin
 * created: 2025/8/15
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class TaskSubChangeStatusReqVO extends BaseRequestVO {
    private static final long serialVersionUID = 1L;

    /**
     * 子任务号
     */
    private String taskSubNo;

    /**
     * 子任务状态
     */
    private Integer status;

}
