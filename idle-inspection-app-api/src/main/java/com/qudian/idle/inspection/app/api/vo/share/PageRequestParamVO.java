package com.qudian.idle.inspection.app.api.vo.share;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2024</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> board)
 * @version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageRequestParamVO extends BaseRequestVO {
    private static final long serialVersionUID = 4080892333276363102L;
    /**
     * 页大小
     */
    private int pageSize = 10;
    /**
     * 当前页
     */
    private int pageNumber = 1;

    public PageRequestParamVO(Long userId, int pageSize, int pageNumber) {
        super(userId);
        this.pageSize = pageSize;
        this.pageNumber = pageNumber;
    }
}
