package com.qudian.idle.inspection.app.api.vo.response.auth;

import com.qudian.idle.inspection.app.api.vo.share.BaseResponseVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EmployeeInfoQueryRespVO extends BaseResponseVO {

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 手机号
     */
    private String mobile;

    /**
     * 名称
     */
    private String name;


    /**
     * 来源系统 1:检测端，2:仓库端
     *
     * @see com.qudian.idle.inspection.app.api.enums.employee.EmployeeSourceSystemEnum
     */
    private Integer sourceSystem;


}
