package com.qudian.idle.inspection.app.api.vo.response.auth;

import com.qudian.idle.inspection.app.api.vo.share.BaseResponseVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class InspectionPermissionQueryRespVO extends BaseResponseVO {

    /**
     * 岗位权限列表
     */
    private List<JobPermission> jobPermissionList;


    @Data
    public static class JobPermission implements Serializable {
        /**
         * 岗位id
         */
        private Long jobId;

        /**
         * 岗位名称
         */
        private String jobName;

        /**
         * 等级
         */
        private Integer level;


    }


}
