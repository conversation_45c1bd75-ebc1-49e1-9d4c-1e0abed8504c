package com.qudian.idle.inspection.app.api.vo.response.task;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * description:
 * author: wangyixin
 * created: 2025/8/14
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class TaskSubDetailRespVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务号
     */
    private String taskNo;

    /**
     * 子任务号
     */
    private String taskSubNo;
    /**
     * 订单号
     */
    private String orderNo;

    /**
     * skuId
     */
    private String skuId;
    /**
     * skuName
     */
    private String skuName;


    /**
     * 品牌id
     */
    private String productBrandId;
    /**
     * 品牌名称
     */
    private String productBrandName;
    /**
     * 商品分类id
     */
    private String productCategoryId;
    /**
     * 商品分类名称
     */
    private String productCategoryName;
    /**
     * 规格名称
     */
    private String productSpecName;
    /**
     * 任务类型
     * 1：真假鉴定；2：瑕疵鉴定；3：成色鉴定；4：拍照
     */
    private Integer taskType;

    /**
     * 子任务状态
     */
    private Integer status;

    /**
     * 是否允许重复步骤：0不允许，1允许（对应 `allow_repeat`）
     */
    private Integer allowRepeat;

    /**
     * sku校对结果
     * 0：sku正确；1：sku错误
     */
    private Integer skuCompResults;


    //sku校对
    /**
     * sku校对状态 SkuRelationStepVo
     */
    private SkuRelationStepVo skuRelationStep;

    /**
     * 步骤列表
     */
    private List<DetectionStepDetailVo> StepList;
    /**
     * 可重复的步骤
     */
    private List<DetectionStepDetailVo> repeatableStepList;

    /**
     * 结果
     */
    private Integer result;

    /**
     * 结果说明
     */
    private String resultDesc;





}
