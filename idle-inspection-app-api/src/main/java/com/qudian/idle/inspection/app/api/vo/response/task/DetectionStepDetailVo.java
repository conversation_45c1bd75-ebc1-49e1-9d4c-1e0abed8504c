// java
package com.qudian.idle.inspection.app.api.vo.response.task;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 检测步骤详情
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class DetectionStepDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 步骤，从1开始，最大999，-1为其他瑕疵（在质检端可重复添加）
     */
    private Integer step;




    /**
     * 位置字段开关
     */
    private Integer positionSwitch;

    /**
     * 位置名称
     */
    private String positionName;

    /**
     * 位置值的类型：1固定值，2预设值
     */
    private Integer positionValueType;

    /**
     * 固定值字符串或预设值 JSON
     */
    private String positionValue;

    /**
     * 字段提示
     */
    private String positionTips;

    /**
     * 字段说明
     */
    private String positionDesc;

    /**
     * 用户输入的位置值
     */
    private String positionInputValue;



    /**
     * 描述字段开关
     */
    private Integer descSwitch;

    /**
     * 描述名称
     */
    private String descName;

    /**
     * 描述提示
     */
    private String descTips;

    /**
     * 描述的描述
     */
    private String descDesc;

    /**
     * 描述值类型：1图文，2文
     */
    private Integer descValueType;

    /**
     * 图文设置\-拍摄照片：1是，0否
     */
    private Integer descGtsPhoto;

    /**
     * 图文设置\-必填：1是，0否
     */
    private Integer descGtsRequired;

    /**
     * 图文设置\-对比图：1是，0否
     */
    private Integer descGtsCompChart;

    /**
     * 图文设置\-照片数量 \> 多少张
     */
    private Integer descGtsPhotoNum;

    /**
     * 图文设置\-文字描述 \< 多少字
     */
    private Integer descGtsTextNum;

    /**
     * 用户输入的描述内容
     */
    private String descInputContent;

    /**
     * 用户输入的照片路径
     */
    private List<String> descInputPhotos;

    /**
     * 用户输入的比对图照片路径
     */
    private List<String> descInputCompPhotos;


    /**
     * 评级开关
     */
    private Integer gradingSwitch;
    /**
     * 评级名称
     */
    private String gradingName;

    /**
     * 评级值类型：1评分，2单选
     */
    private Integer gradingValueType;

    /**
     * 评级提示
     */
    private String gradingTips;

    /**
     * 评级描述
     */
    private String gradingDesc;

    /**
     * 评级分数
     */
    private Integer gradingScore;

    /**
     * 评级分数描述
     */
    private String gradingScoreDesc;

    /**
     * 评级单选
     */
    private List<GradingRadioItemVo> gradingRadio;

    /**
     * 用户输入的评级结果（
     */
    private Integer gradingInputResult;

    /**
     * 用户输入的评级结果描述
     */
    private String gradingInputResultDesc;
}