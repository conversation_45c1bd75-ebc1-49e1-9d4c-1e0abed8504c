package com.qudian.idle.inspection.app.api.vo.request.task;

import com.qudian.pdt.api.toolkit.vo.request.BaseRequestVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 任务认领请求
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class TaskClaimReqVO extends BaseRequestVO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 任务号
     */
    private String taskNo;

    /**
     * 任务号
     */
    private List<String> taskNos;

}