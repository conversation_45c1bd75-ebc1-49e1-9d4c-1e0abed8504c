package com.qudian.idle.inspection.app.api.facade.certificate;

import com.qudian.idle.inspection.app.api.vo.request.certificate.CertificateBaseShowReqVO;
import com.qudian.idle.inspection.app.api.vo.response.certificate.CertificateBaseShowRespVO;
import com.qudian.lme.common.dto.BaseResponseDTO;

/**
 * <p>文件名称:com.qudian.idle.inspection.app.api.facade.certificate.CertificatesFacade</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/21
 */
public interface CertificatesFacade {

    /**
     * 查看鉴定证书详情
     *
     * @param reqVO
     * @return {@link BaseResponseDTO }<{@link CertificateBaseShowRespVO }>
     */
    BaseResponseDTO<CertificateBaseShowRespVO> show(CertificateBaseShowReqVO reqVO);
}
