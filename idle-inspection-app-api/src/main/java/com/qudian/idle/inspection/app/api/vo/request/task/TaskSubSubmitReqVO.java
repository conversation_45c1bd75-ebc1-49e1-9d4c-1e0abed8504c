package com.qudian.idle.inspection.app.api.vo.request.task;

import com.qudian.idle.inspection.app.api.vo.response.task.DetectionStepDetailVo;
import com.qudian.idle.inspection.app.api.vo.response.task.SkuRelationStepVo;
import com.qudian.pdt.api.toolkit.vo.request.BaseRequestVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * description:
 * author: wangyixin
 * created: 2025/8/14
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class TaskSubSubmitReqVO extends BaseRequestVO {

    /**
     * 子任务号
     */
    private String taskSubNo;

    /**
     * sku校对结果
     * 0：sku正确；1：sku错误
     */
    private Integer skuCompResults;


    //sku校对
    /**
     * sku校对状态 SkuRelationStepVo
     */
    private SkuRelationStepVo skuRelationStep;

    /**
     * 步骤列表
     */
    private List<DetectionStepDetailVo> StepList;
    /**
     * 可重复的步骤
     */
    private List<DetectionStepDetailVo> repeatableStepList;

    /**
     * 结果
     */
    private Integer result;

    /**
     * 结果说明
     */
    private String resultDesc;

}
