package com.qudian.idle.inspection.app.api.facade;

import com.qudian.idle.inspection.app.api.vo.request.auth.AuthenticationReqVO;
import com.qudian.idle.inspection.app.api.vo.request.auth.EmployeeInfoQueryReqVO;
import com.qudian.idle.inspection.app.api.vo.request.auth.InspectionPermissionQueryReqVO;
import com.qudian.idle.inspection.app.api.vo.request.auth.LoginBySmsCodeReqVO;
import com.qudian.idle.inspection.app.api.vo.request.auth.SendVerificationCodeReqVO;
import com.qudian.idle.inspection.app.api.vo.response.auth.AuthenticationRespVO;
import com.qudian.idle.inspection.app.api.vo.response.auth.EmployeeInfoQueryRespVO;
import com.qudian.idle.inspection.app.api.vo.response.auth.InspectionPermissionQueryRespVO;
import com.qudian.idle.inspection.app.api.vo.response.auth.LoginBySmsCodeRespVO;
import com.qudian.idle.inspection.app.api.vo.response.common.CommandResultRespVO;
import com.qudian.lme.common.dto.BaseResponseDTO;

public interface AuthFacade {

    /**
     * 发送验证码
     * @param command
     * @return
     */
    BaseResponseDTO<CommandResultRespVO<Object>> sendVerificationCode(SendVerificationCodeReqVO command);


    /**
     * 登陆
     * @param command
     * @return
     */
    BaseResponseDTO<LoginBySmsCodeRespVO> loginBySmsCode(LoginBySmsCodeReqVO command);

    /**
     * 权限校验
     * @param command
     * @return
     */
    BaseResponseDTO<AuthenticationRespVO> authentication(AuthenticationReqVO command);

    /**
     * 查询员工信息
     * @param query
     * @return
     */
    BaseResponseDTO<EmployeeInfoQueryRespVO> queryEmployeeInfo(EmployeeInfoQueryReqVO query);

    /**
     * 员工权限查询
     * @param query
     * @return
     */
    BaseResponseDTO<InspectionPermissionQueryRespVO> inspectionPermissionQuery(InspectionPermissionQueryReqVO query);

}
