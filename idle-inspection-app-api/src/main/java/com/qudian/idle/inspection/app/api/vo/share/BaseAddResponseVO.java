package com.qudian.idle.inspection.app.api.vo.share;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class BaseAddResponseVO<ID> extends BaseResponseVO {

    /**
     * id
     */
    private ID id;


    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误信息
     */
    private String failMessage;


}
