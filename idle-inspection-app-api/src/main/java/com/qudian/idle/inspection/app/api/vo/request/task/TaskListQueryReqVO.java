// java
package com.qudian.idle.inspection.app.api.vo.request.task;

import com.qudian.idle.inspection.app.api.vo.response.auth.InspectionPermissionQueryRespVO;
import com.qudian.pdt.api.toolkit.vo.request.BaseRequestVO;
import com.qudian.pdt.api.toolkit.vo.request.PageRequestVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务列表查询请求
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class TaskListQueryReqVO extends PageRequestVO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 任务号
     */
    private String taskNo;

    /**
     * 截止开始时间
     */
    private LocalDateTime deadlineStartTime;

    /**
     * 截止结束时间
     */
    private LocalDateTime deadlineEndTime;

    /**
     * 品牌id
     */
    private String productBrandId;

    /**
     * 商品分类id
     */
    private String productCategoryId;


    /**
     * 任务分类id，如1真假鉴定、2瑕疵鉴定、3成色鉴定、4拍照等
     */
    private Long taskCategoryId;

    /**
     * 岗位权限列表
     */
    private List<InspectionPermissionQueryRespVO.JobPermission> jobPermissionList;
}