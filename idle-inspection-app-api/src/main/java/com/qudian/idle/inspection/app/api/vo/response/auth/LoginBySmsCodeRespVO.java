package com.qudian.idle.inspection.app.api.vo.response.auth;

import com.qudian.idle.inspection.app.api.vo.response.common.CommandResultRespVO;
import com.qudian.pdt.api.toolkit.vo.response.BaseResponseVO;
import lombok.Data;

import java.io.Serializable;

@Data
public class LoginBySmsCodeRespVO implements Serializable {

    private static final long serialVersionUID = 4571151080901326924L;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误信息
     */
    private String failedMessage;

    /**
     * 登录令牌
     */
    private String token;
    /**
     * 用户id
     */
    private Long userId;


    /**
     * 来源系统 1:检测端，2:仓库端
     *
     * @see com.qudian.idle.inspection.app.api.enums.employee.EmployeeSourceSystemEnum
     */
    private Integer sourceSystem;
}
