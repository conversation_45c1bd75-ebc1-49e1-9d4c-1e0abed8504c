package com.qudian.idle.inspection.app.api.enums.employee;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;


@Getter
@AllArgsConstructor
public enum EmployeeSourceSystemEnum {

    INSPECTION(1,"inspection", "检测端"),
    WMS(2,"wms","仓库端"),
    ;

    /**
     * 编码
     */
    private int code;

    /**
     * 标识
     */
    private String key;

    /**
     * 描述
     */
    private String desc;

    /**
     * 根据 code 判断是否是该枚举
     *
     * @param code code
     * @return true:是，false:否
     */
    public boolean same(Integer code) {
        return Objects.equals(this.code, code);
    }

    /**
     * 根据 code 获取对应枚举值
     *
     * @param code code
     * @return 对应枚举，匹配不到返回null
     */
    public static EmployeeSourceSystemEnum getByCode(Integer code) {
        for (EmployeeSourceSystemEnum value : values()) {
            if (value.same(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据 code 获取对应枚举值
     *
     * @param code        code
     * @param defaultEnum 默认值
     * @return 对应枚举，匹配不到返回 defaultEnum
     */
    public static EmployeeSourceSystemEnum getByCode(Integer code, EmployeeSourceSystemEnum defaultEnum) {
        EmployeeSourceSystemEnum result = getByCode(code);
        return result != null ? result : defaultEnum;
    }
}
