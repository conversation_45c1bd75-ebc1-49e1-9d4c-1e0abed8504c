package com.qudian.idle.inspection.app.api.vo.share;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2024</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> board)
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PagingList<T> implements Serializable {
    private static final long serialVersionUID = -5204034592643253845L;
    /**
     * 总数
     */
    private long total;
    /**
     * 每页的数量
     */
    private long pageSize;
    /**
     * 当前页
     */
    private long pageNum;
    /**
     * 是否还有下一页
     */
    private boolean hasMore;
    /**
     * 结果对象
     */
    private List<T> resultList;
}
