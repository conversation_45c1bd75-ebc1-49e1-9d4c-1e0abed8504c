package com.qudian.idle.inspection.app.api.vo.request.auth;

import lombok.Data;

import java.io.Serializable;

@Data
public class SendVerificationCodeReqVO implements Serializable {

    /**
     * 手机号或者邮箱
     */
    private String emailOrMobile;
    /**
     * 使用手机验证的code还是邮箱， 值是email和mobile两种
     */
    private String authType;
    /**
     * 发送验证码是什么场景发送的 LOGIN
     */
    private String sendType;

    /**
     * 平台
     */
    private String platform;

    /**
     * 来源系统
     */
    private Integer sourceSystem;

}
