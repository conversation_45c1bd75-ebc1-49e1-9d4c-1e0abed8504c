// java
package com.qudian.idle.inspection.app.api.vo.response.task;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 评级单选项
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class GradingRadioItemVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    // 值
    @JsonProperty("value")
    private String value;

    // 需要文本说明 1需要 0不需要
    @JsonProperty("need_text_desc")
    private Integer needTextDesc;
}