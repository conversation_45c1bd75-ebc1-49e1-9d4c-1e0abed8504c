package com.qudian.idle.inspection.app.api.vo.request;

import com.qudian.pdt.api.toolkit.vo.request.BaseRequestVO;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * <p>文件描述: 测试服务连通性-请求体</p>
 * <p>版权所有: Copyright(C)2019-2024</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> architecture board)
 * @version 1.0
 */
@Data
public class ValleyEchosReqVO extends BaseRequestVO {
    private static final long serialVersionUID = 3239370130333521183L;
    @NotNull(message = "validate.msg")
    private String heyRoar;
    private String origin;
}
